﻿namespace Lighthouse.Controllers.MasterData
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CargoController : ControllerBase
    {
        private readonly ICargoService _cargoService;
        private readonly IUserService _userService;

        public CargoController(ICargoService cargoService, IUserService userService)
        {
            _cargoService = cargoService;
            _userService = userService;
        }

        [HttpPost("filter")]
        [ProducesResponseType(200, Type = typeof(IList<CargoModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll(
            [FromBody] MaintenanceCargoFilterModel filterModel)
        {
            var cargos = new List<CargoModel>();
            if (filterModel.ReturnApprovedOnly)
            {
                cargos = await _cargoService.GetAllApprovedAsync();
            }
            else
            {
                cargos = await _cargoService.GetAllAsync(filterModel);
            }
            return Ok(cargos);
        }

        [HttpPost("getCargos")]
        [ProducesResponseType(200, Type = typeof(IList<CargoModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll([FromBody] CcuFilter filter, [FromQuery] bool isContain = false)
        {
            var cargos = await _cargoService.GetAllCCUsAsync(User, filter, isContain);
            return Ok(cargos);
        }

        [HttpGet("getCargoDescriptions")]
        [ProducesResponseType(200, Type = typeof(IList<CargoUnitTypeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetCargoDescriptions()
        {
            var descriptions = await _cargoService.GetCargoDescriptions();
            return Ok(descriptions);
        }

        [HttpGet("getCargosIncludingAdhoc")]
        [ProducesResponseType(200, Type = typeof(IList<CargoModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetCargosIncludingAdhocAsync()
        {
            var user = await _userService.GetCurrentUser(User);
            var cargos = await _cargoService.GetAllAsyncIncludingAdhocForContain(user);
            return Ok(cargos);
        }

        [HttpGet("listbyccu")]
        [ProducesResponseType(200, Type = typeof(IList<CargoModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByCCU(Guid locationId, string ccuIdPart)
        {
            var cargos = await _cargoService.GetAllByCCUAsync(locationId, ccuIdPart);
            return Ok(cargos);
        }

        [HttpGet("validateforextra/{voyageId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ValidateForExtraAsync(Guid voyageId, [FromQuery] string ccuId)
        {
            var isValid = await _cargoService.ValidateForExtraAsync(voyageId, ccuId);
            return Ok(new { IsValid = isValid });
        }

        [HttpGet("bylocation/{locationId}")]
        [ProducesResponseType(200, Type = typeof(IList<CargoModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllByLocation(Guid locationId)
        {
            var cargos = await _cargoService.GetAllByLocationAsync(locationId);
            return Ok(cargos);
        }

        [HttpGet("byTR/{transportRequestId}")]
        [ProducesResponseType(200, Type = typeof(IList<CargoModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllCargoesByTR(Guid transportRequestId)
        {
            var cargos = await _cargoService.GetAllCargoesByTRAsync(transportRequestId);
            return Ok(cargos);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Get(Guid id)
        {
            var cargo = await _cargoService.GetByIdAsync(id);
            return Ok(cargo);
        }

        [HttpGet("getbyccu/{ccuId}")]
        [ProducesResponseType(200, Type = typeof(CargoModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Get(string ccuId)
        {
            var cargo = await _cargoService.GetByCcuIdAsync(ccuId);
            return Ok(cargo);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CargoModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] CargoUpsertModel model)
        {
            var cargo = await _cargoService.CreateAsync(model, User);

            if (cargo is null)
            {
                return BadRequest();
            }

            return Ok(cargo);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CargoModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] CargoUpsertModel model)
        {
            var cargo = await _cargoService.UpdateAsync(id, model, User);

            if (cargo is null)
            {
                return BadRequest();
            }

            return Ok(cargo);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await _cargoService.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("approveCargo/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> ApproveCargo(Guid id)
        {
            await _cargoService.ApproveCargo(id, User);
            return Ok();
        }

        [HttpPost("enableDisable/{id}")]
        [ProducesResponseType(200, Type = typeof(CargoModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> EnableDisableCargo(Guid id)
        {
            var cargo = await _cargoService.EnableDisableCargo(id, User);
            return Ok(cargo);
        }

        [HttpPut("setCcuStatus/{id}")]
        public async Task<IActionResult> SetCcuStatus(Guid id, [FromBody] SetCcuStatusModel model)
        {
            if (model == null)
            {
                return BadRequest("Status is required.");
            }

            var cargo = await _cargoService.SetCcuStatusAsync(id, model.Status, User);

            if (cargo == null)
            {
                return BadRequest();
            }

            return Ok(cargo);
        }

        [HttpPost("setCertificateTestDate")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> SetCertificateTestDate([FromBody] CertificateTestDateModel model)
        {
            await _cargoService.SetCertificateTestDate(model);
            return Ok();
        }
    }
}

