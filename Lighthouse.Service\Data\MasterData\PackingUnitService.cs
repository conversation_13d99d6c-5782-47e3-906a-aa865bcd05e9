﻿namespace Lighthouse.Service.Data.MasterData
{
    public class PackingUnitService : IPackingUnitService
    {
        private readonly IUnitOfWork unitOfWork;
        private readonly IMapper mapper;

        public PackingUnitService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            this.unitOfWork = unitOfWork;
            this.mapper = mapper;
        }

        public async Task<IList<PackingUnitModel>> GetAsync(PackingUnitSearchModel model)
        {
            var query = unitOfWork.Repository<PackingUnit>()
                .Query()
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .AsNoTracking();

            if (!string.IsNullOrEmpty(model?.Query))
            {
                query = query.Where(x => x.Name.Contains(model.Query));
            }

            return (await query.OrderByDescending(x => x.CreatedDate).ToListAsync()).Select(mapper.Map<PackingUnitModel>).ToList();
        }

        public async Task<PackingUnitModel> GetPackingUnitByIdAsync(Guid id)
        {
            var unit = await unitOfWork.Repository<PackingUnit>()
                .Query()
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.PackingUnitId == id);

            return mapper.Map<PackingUnitModel>(unit);
        }

        public async Task<PackingUnitModel> PostAsync(PackingUnitUpsertModel model, Guid userId)
        {
            ValidateUpsertModel(model);

            if (await AlreadyExists(model.Name, null)) throw new InvalidOperationException("The Packing Unit already exists");

            var unit = await unitOfWork.Repository<PackingUnit>().CreateAsync(mapper.Map<PackingUnit>(model));

            unit.CreatedById = userId;
            unit.UpdatedById = userId;

            await unitOfWork.SaveChangesAsync();

            return mapper.Map<PackingUnitModel>(unit);
        }

        public async Task<PackingUnitModel> PutAsync(Guid id, PackingUnitUpsertModel model, Guid userId)
        {
            ValidateUpsertModel(model);

            var unit = await unitOfWork.Repository<PackingUnit>().FirstOrDefaultAsync(x => x.PackingUnitId == id);
            if (unit is null) throw new InvalidOperationException("The specified packaging unit was not found");

            if (await AlreadyExists(model.Name, id)) throw new InvalidOperationException("The Packing Unit already exists");

            if (await IsInUse(id)) throw new InvalidOperationException("Packing Unit can not be updated, because is used in cargo table");

            unit = mapper.Map(model, unit);
            unit.UpdatedById = userId;
            unit.UpdatedDate = DateTime.UtcNow;
            unitOfWork.Repository<PackingUnit>().Update(unit);
            await unitOfWork.SaveChangesAsync();

            return mapper.Map<PackingUnitModel>(unit);
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            if (await IsInUse(id)) throw new InvalidOperationException("Packing Unit can not be disabled, as it is used in cargo");

            var unit = await unitOfWork.Repository<PackingUnit>().Query(x => x.PackingUnitId == id).FirstOrDefaultAsync();
            if (unit != null)
            {
                try
                {
                    unit.Deleted = true;
                    unitOfWork.Repository<PackingUnit>().Update(unit);
                    await unitOfWork.SaveChangesAsync();
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            return false;
        }

        private async Task<bool> AlreadyExists(string name, Guid? packingUnitId)
        {
            return
                await unitOfWork.Repository<PackingUnit>().Query().AnyAsync(u => u.Name == name && u.PackingUnitId != packingUnitId);
        }

        private async Task<bool> IsInUse(Guid packingUnitId)
        {
            return
                await unitOfWork.Repository<VoyageMaterialDetail>().Query().AnyAsync(m => m.PackingUnitId == packingUnitId) ||
                await unitOfWork.Repository<TransportRequestMaterialDetail>().Query().AnyAsync(m => m.PackingUnitId == packingUnitId);
        }

        private static void ValidateUpsertModel(PackingUnitUpsertModel model)
        {
            string error = "";

            if (string.IsNullOrEmpty(model.Name))
            {
                error = $"{nameof(model.Name)} is required";
            }
            else if (model.Name.Length > 100)
            {
                error = $"{nameof(model.Name)} cannot be longer than 100 characters";
            }
            else if (model.Name.Contains('-'))
            {
                error = $"{nameof(model.Name)} cannot contain dashes";
            }
            else if (model.Description.Length > 255)
            {
                error = $"{nameof(model.Description)} cannot be longer than 255 characters";
            }

            if (error != "") throw new InvalidOperationException(error);
        }

    }
}