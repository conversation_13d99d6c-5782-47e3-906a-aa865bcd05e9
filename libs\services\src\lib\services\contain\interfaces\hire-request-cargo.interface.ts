import { Cargo } from "../../maintenance/interfaces/cargoes.interface";

export interface HireRequestCargo {
    hireRequestCargoId: string;
    cargoId: string;
    cargoName: string;
    cargoCCUId: string;
    cargoOwner: string;
    cargoDescription: string;
    cargoUnitType: string;
    hireRequestId?: string;
    hireCreated: boolean;
    vendorId: string;
    vendorVendorName: string;
    cargoVendorId: string;
    cargoCcuId: string;
    clientId: string;
    clientName: string;
    assetId: string;
    assetName: string;
    reference: string;
    vendorOutbound: string;
    vendorInbound: string;
    longTermHire: boolean;
    shelvesSupplied: boolean;
    shelvesReturned: boolean;
    netSupplied: boolean;
    netReturned: boolean;
    tarpaulinSupplied: boolean;
    tarpaulinReturned: boolean;
    comments?: string;
    isHired?: boolean;
    onHiredDate?: Date;
    deleted: boolean;
    createdById: string;
    createdByName: string;
    createdDate: Date;
    updatedById: string;
    updatedByName: string;
    updatedDate?: Date;
    vendorOutboundDate?: Date;
    vendorInboundDate?: Date;
    shipped?: Date;
    returned?: Date;
    offHiredDate?: Date;
    billingAssetId: string;
    billingAssetName: string;
    manifestOut: string;
    manifestIn: string;
    consignmentNumber: string;
    containerPoolId: string;
    containerPoolName: string;
    cargo?: Cargo;
    isPool: boolean;
}