﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lighthouse.Data.Migrations
{
    /// <inheritdoc />
    public partial class MaterialDetails_PackingUnit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PackingUnit",
                table: "VoyageMaterialDetails");

            migrationBuilder.DropColumn(
                name: "PackagingUnit",
                table: "TransportRequestMaterialDetails");

            migrationBuilder.AddColumn<Guid>(
                name: "PackingUnitId",
                table: "VoyageMaterialDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PackingUnitId",
                table: "TransportRequestMaterialDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "PackingUnits",
                columns: table => new
                {
                    PackingUnitId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    CreatedById = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UpdatedById = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PackingUnits", x => x.PackingUnitId);
                    table.ForeignKey(
                        name: "FK_PackingUnits_Users_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PackingUnits_Users_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "Users",
                        principalColumn: "UserId");
                });

            migrationBuilder.CreateIndex(
                name: "IX_VoyageMaterialDetails_PackingUnitId",
                table: "VoyageMaterialDetails",
                column: "PackingUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_TransportRequestMaterialDetails_PackingUnitId",
                table: "TransportRequestMaterialDetails",
                column: "PackingUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_PackingUnits_CreatedById",
                table: "PackingUnits",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_PackingUnits_UpdatedById",
                table: "PackingUnits",
                column: "UpdatedById");

            migrationBuilder.AddForeignKey(
                name: "FK_TransportRequestMaterialDetails_PackingUnits_PackingUnitId",
                table: "TransportRequestMaterialDetails",
                column: "PackingUnitId",
                principalTable: "PackingUnits",
                principalColumn: "PackingUnitId");

            migrationBuilder.AddForeignKey(
                name: "FK_VoyageMaterialDetails_PackingUnits_PackingUnitId",
                table: "VoyageMaterialDetails",
                column: "PackingUnitId",
                principalTable: "PackingUnits",
                principalColumn: "PackingUnitId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TransportRequestMaterialDetails_PackingUnits_PackingUnitId",
                table: "TransportRequestMaterialDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_VoyageMaterialDetails_PackingUnits_PackingUnitId",
                table: "VoyageMaterialDetails");

            migrationBuilder.DropTable(
                name: "PackingUnits");

            migrationBuilder.DropIndex(
                name: "IX_VoyageMaterialDetails_PackingUnitId",
                table: "VoyageMaterialDetails");

            migrationBuilder.DropIndex(
                name: "IX_TransportRequestMaterialDetails_PackingUnitId",
                table: "TransportRequestMaterialDetails");

            migrationBuilder.DropColumn(
                name: "PackingUnitId",
                table: "VoyageMaterialDetails");

            migrationBuilder.DropColumn(
                name: "PackingUnitId",
                table: "TransportRequestMaterialDetails");

            migrationBuilder.AddColumn<string>(
                name: "PackingUnit",
                table: "VoyageMaterialDetails",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PackagingUnit",
                table: "TransportRequestMaterialDetails",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);
        }
    }
}
