﻿// <auto-generated />
using System;
using Lighthouse.Data.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Lighthouse.Data.Migrations
{
    [DbContext(typeof(DataContext))]
    partial class DataContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Lighthouse.Model.Entity.Activity", b =>
                {
                    b.Property<Guid>("ActivityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Chargeability")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ActivityId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Activities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ActivityCategory", b =>
                {
                    b.Property<Guid>("ActivityCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ActivityType")
                        .HasColumnType("int");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsHidden")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ActivityCategoryId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ActivityCategories");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ActivityCategoryType", b =>
                {
                    b.Property<Guid>("ActivityCategoryTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActivityCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("CreateOutOfPortActivity")
                        .HasColumnType("bit");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInboundLifting")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ActivityCategoryTypeId");

                    b.HasIndex("ActivityCategoryId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ActivityCategoryTypes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Area", b =>
                {
                    b.Property<Guid>("AreaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("AreaId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("SiteId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Areas");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.AreaBlockingActivity", b =>
                {
                    b.Property<Guid>("AreaBlockingActivityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlockingActivityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("EndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("AreaBlockingActivityId");

                    b.HasIndex("AreaId");

                    b.HasIndex("BlockingActivityId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("AreaBlockingActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Asset", b =>
                {
                    b.Property<Guid>("AssetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AssetType")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ClusterHeadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Color")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("AssetId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Assets");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.AssetLocation", b =>
                {
                    b.Property<Guid>("AssetLocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("AssetLocationId");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("AssetLocations");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.AuditLog", b =>
                {
                    b.Property<Guid>("AuditLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FieldName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NewValue")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OldValue")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PrimaryIndicator")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<string>("TableName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TypeOfAction")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("AuditLogId");

                    b.HasIndex("UserId");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BillingPeriod", b =>
                {
                    b.Property<Guid>("BillingPeriodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BillingPeriodMonth")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("DateCompleted")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsLocked")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSplitBilling")
                        .HasColumnType("bit");

                    b.Property<DateTime>("PeriodEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("PeriodStartDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Recalculate")
                        .HasColumnType("bit");

                    b.Property<string>("Status")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("BillingPeriodId");

                    b.ToTable("BillingPeriods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BillingPeriodDocument", b =>
                {
                    b.Property<Guid>("BillingPeriodDocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BillingPeriodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Revision")
                        .HasColumnType("int");

                    b.HasKey("BillingPeriodDocumentId");

                    b.HasIndex("BillingPeriodId");

                    b.HasIndex("CreatedById");

                    b.ToTable("BillingPeriodDocuments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BlockingActivity", b =>
                {
                    b.Property<Guid>("BlockingActivityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("BlockingActivityId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("BlockingActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BulkRequest", b =>
                {
                    b.Property<Guid>("BulkRequestId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BilledAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BulkTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<double?>("OverChargeCost")
                        .HasColumnType("float");

                    b.Property<double>("Quantity")
                        .HasColumnType("float");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("BulkRequestId");

                    b.HasIndex("BilledAssetId");

                    b.HasIndex("BulkTypeId");

                    b.HasIndex("ClientId");

                    b.HasIndex("VoyageId");

                    b.ToTable("BulkRequests");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BulkTransaction", b =>
                {
                    b.Property<Guid>("BulkTransactionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BulkTransactionNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("BulkTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<DateTime?>("CompletedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<double>("Cost")
                        .HasColumnType("float");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<double?>("DayRatePrice")
                        .HasColumnType("float");

                    b.Property<string>("DelTicket")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<double?>("Quantity")
                        .HasColumnType("float");

                    b.Property<Guid>("TankTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TransactionType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("BulkTransactionId");

                    b.HasIndex("AssetId");

                    b.HasIndex("BulkTypeId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TankTypeId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("BulkTransactions");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BulkType", b =>
                {
                    b.Property<Guid>("BulkTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("FluidType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("BulkTypeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("BulkTypes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Cargo", b =>
                {
                    b.Property<Guid>("CargoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CCUId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CargoDescriptionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("CargoStatus")
                        .HasColumnType("int");

                    b.Property<int?>("Category")
                        .HasColumnType("int");

                    b.Property<int>("CcuHireStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateOnly?>("CertificateTestDate")
                        .HasColumnType("date");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("Disabled")
                        .HasColumnType("bit");

                    b.Property<Guid?>("FamilyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("HeightMm")
                        .HasColumnType("float");

                    b.Property<bool>("IsAdhoc")
                        .HasColumnType("bit");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeckCargo")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPool")
                        .HasColumnType("bit");

                    b.Property<double>("LengthMm")
                        .HasColumnType("float");

                    b.Property<Guid?>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("MaxGrossWeightKg")
                        .HasColumnType("float");

                    b.Property<Guid?>("PoolId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SizeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("TareMassKg")
                        .HasColumnType("float");

                    b.Property<Guid?>("TypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("WidthMm")
                        .HasColumnType("float");

                    b.HasKey("CargoId");

                    b.HasIndex("CargoDescriptionId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FamilyId");

                    b.HasIndex("LocationId");

                    b.HasIndex("PoolId");

                    b.HasIndex("SizeId");

                    b.HasIndex("TypeId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.ToTable("Cargos");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoCertificate", b =>
                {
                    b.Property<Guid>("CargoCertificateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("TestDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("CargoCertificateId");

                    b.HasIndex("CargoId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("CargoCertificates");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoDescription", b =>
                {
                    b.Property<Guid>("CargoDescriptionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAdhoc")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("CargoDescriptionId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("CargoDescriptions");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoEvent", b =>
                {
                    b.Property<Guid>("CargoEventId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CargoOwnerEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CargoOwnerStartDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EventDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EventType")
                        .HasColumnType("int");

                    b.Property<Guid>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("CargoEventId");

                    b.HasIndex("CargoId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.ToTable("CargoEvents");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoFamily", b =>
                {
                    b.Property<Guid>("CargoFamilyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("Disabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("CargoFamilyId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("CargoFamilies");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoSize", b =>
                {
                    b.Property<Guid>("CargoSizeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CargoFamilyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("Disabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("CargoSizeId");

                    b.HasIndex("CargoFamilyId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("CargoSizes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoType", b =>
                {
                    b.Property<Guid>("CargoTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CargoFamilyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("Disabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("CargoTypeId");

                    b.HasIndex("CargoFamilyId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("CargoTypes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Client", b =>
                {
                    b.Property<Guid>("ClientId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ClientLogoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("CustomsCompliant")
                        .HasColumnType("bit");

                    b.Property<string>("EUNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VATNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientAsset", b =>
                {
                    b.Property<Guid>("ClientAssetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("EndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsClusterHead")
                        .HasColumnType("bit");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ClientAssetId");

                    b.HasIndex("AssetId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ClientAssets");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientBillingPeriodTimeAllocation", b =>
                {
                    b.Property<Guid>("ClientBillingPeriodTimeAllocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BillingPeriodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("ChargeableTime")
                        .HasColumnType("float");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("DedicatedTime")
                        .HasColumnType("float");

                    b.Property<double>("FuelPercentage")
                        .HasColumnType("float");

                    b.Property<double>("NonChargeablePercentage")
                        .HasColumnType("float");

                    b.Property<double>("NonChargeableTime")
                        .HasColumnType("float");

                    b.HasKey("ClientBillingPeriodTimeAllocationId");

                    b.HasIndex("BillingPeriodId");

                    b.HasIndex("ClientId");

                    b.ToTable("ClientBillingPeriodTimeAllocations");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientLocation", b =>
                {
                    b.Property<Guid>("ClientLocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ClientLocationId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ClientLocations");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientNameHistory", b =>
                {
                    b.Property<Guid>("ClientNameHistoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("EndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ClientNameHistoryId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ClientNameHistory");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientReportType", b =>
                {
                    b.Property<Guid>("ClientReportTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("ReportTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ClientReportTypeId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ReportTypeId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ClientReportTypes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClusterHistory", b =>
                {
                    b.Property<Guid>("ClusterHistoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClusterChildId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClusterHeadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("EndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ClusterHistoryId");

                    b.HasIndex("ClusterChildId");

                    b.HasIndex("ClusterHeadId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ClusterHistory");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CommentReadByUser", b =>
                {
                    b.Property<Guid>("CommentReadByUserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("ReaderUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SailingRequestUserCommentId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("CommentReadByUserId");

                    b.HasIndex("ReaderUserId");

                    b.HasIndex("SailingRequestUserCommentId");

                    b.ToTable("CommentReadByUsers");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Crane", b =>
                {
                    b.Property<Guid>("CraneId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("MaximumRadius")
                        .HasColumnType("float");

                    b.Property<double>("MaximumWeight")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("RadiusForMaximumWeight")
                        .HasColumnType("float");

                    b.Property<string>("Type")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("WeightForMaximumRadius")
                        .HasColumnType("float");

                    b.HasKey("CraneId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Cranes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.DangerousGood", b =>
                {
                    b.Property<Guid>("DangerousGoodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Class")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<int>("PackingGroup")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("ProperShippingName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SubClass")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UnNo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("DangerousGoodId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("DangerousGoods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.DangerousGoodLocation", b =>
                {
                    b.Property<Guid>("DangerousGoodLocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("DangerousGoodLocationId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DangerousGoodId");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("DangerousGoodLocations");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.DeckUsage", b =>
                {
                    b.Property<Guid>("DeckUsageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsCargoIn")
                        .HasColumnType("bit");

                    b.Property<int?>("NumberOfLifts")
                        .HasColumnType("int");

                    b.Property<double?>("TotalWeight")
                        .HasColumnType("float");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("DeckUsageId");

                    b.HasIndex("AssetId");

                    b.HasIndex("ClientId");

                    b.HasIndex("VoyageId");

                    b.ToTable("DeckUsage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Distance", b =>
                {
                    b.Property<Guid>("DistanceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BaseAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<double>("DistanceInMiles")
                        .HasColumnType("float");

                    b.Property<Guid>("ToAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("DistanceId");

                    b.HasIndex("BaseAssetId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ToAssetId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Distances");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.District", b =>
                {
                    b.Property<Guid>("DistrictId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("DistrictName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("DistrictId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("District");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Driver", b =>
                {
                    b.Property<Guid>("DriverId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsAdhoc")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TrailerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VehicleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VendorWarehouseId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("DriverId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("TrailerId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VehicleId");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorWarehouseId");

                    b.ToTable("Drivers");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Employee", b =>
                {
                    b.Property<Guid>("EmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("EmployeeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Employee");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireRequest", b =>
                {
                    b.Property<Guid>("HireRequestId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AwaitingCollection")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("CSTRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<Guid>("CargoDescriptionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CollectionReference")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Comments")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("ConfirmedBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("DoorsRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsCancelled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsHighlighted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<Guid?>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("NetRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("OffWaitingListDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OrderTakenBy")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("PlannedSailingDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("RemovableSidesRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("RequestedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("RequestedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("ShelvesRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("TarpaulinRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("UnitQuantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("VerifiedDateTime")
                        .HasColumnType("datetime2");

                    b.HasKey("HireRequestId");

                    b.HasIndex("AssetId");

                    b.HasIndex("CargoDescriptionId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.ToTable("HireRequests");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireRequestCargo", b =>
                {
                    b.Property<Guid>("HireRequestCargoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("BillingAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("ConsignmentNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("ContainerPoolId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("HireCreated")
                        .HasColumnType("bit");

                    b.Property<Guid?>("HireRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("IsHired")
                        .HasColumnType("bit");

                    b.Property<bool>("LongTermHire")
                        .HasColumnType("bit");

                    b.Property<string>("ManifestIn")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ManifestOut")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<bool>("NetReturned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("NetSupplied")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("OffHiredDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("OnHiredDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Reference")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("Returned")
                        .HasColumnType("datetime2");

                    b.Property<bool>("ShelvesReturned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("ShelvesSupplied")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("Shipped")
                        .HasColumnType("datetime2");

                    b.Property<bool>("TarpaulinReturned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("TarpaulinSupplied")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("VendorInbound")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("VendorInboundDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VendorOutbound")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("VendorOutboundDate")
                        .HasColumnType("datetime2");

                    b.HasKey("HireRequestCargoId");

                    b.HasIndex("AssetId");

                    b.HasIndex("BillingAssetId");

                    b.HasIndex("CargoId");

                    b.HasIndex("ClientId");

                    b.HasIndex("ContainerPoolId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("Deleted");

                    b.HasIndex("HireRequestId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.ToTable("HireRequestCargos");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireRequestCargoEvent", b =>
                {
                    b.Property<Guid>("HireRequestCargoEventId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EventDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EventType")
                        .HasColumnType("int");

                    b.Property<Guid>("HireRequestCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("HireRequestCargoEventId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("HireRequestCargoId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("HireRequestCargoEvents");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireStatement", b =>
                {
                    b.Property<Guid>("HireStatementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("DayRate")
                        .HasColumnType("float");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime>("DeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeliveryPlace")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("Duration")
                        .HasColumnType("float");

                    b.Property<bool>("IsOnHire")
                        .HasColumnType("bit");

                    b.Property<DateTime>("RedeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("RedeliveryPlace")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Type")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VesselId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("HireStatementId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VesselId");

                    b.ToTable("HireStatements");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireStatementBulk", b =>
                {
                    b.Property<Guid>("HireStatementBulkId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BulkTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateLoaded")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<double?>("EndQuantity")
                        .HasColumnType("float");

                    b.Property<Guid>("HireStatementId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Price")
                        .HasColumnType("float");

                    b.Property<double?>("StartQuantity")
                        .HasColumnType("float");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("HireStatementBulkId");

                    b.HasIndex("BulkTypeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("HireStatementId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("HireStatementBulks");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LiftingPlan", b =>
                {
                    b.Property<Guid>("LiftingPlanId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AreHandSignalsUsed")
                        .HasColumnType("bit");

                    b.Property<Guid>("AreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CraneId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("DoAllConditionsApply")
                        .HasColumnType("bit");

                    b.Property<string>("FirstColorCode")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<bool?>("IsNonRoutineLiftPlanAttachedToVoyage")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRadioUsed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRoutine")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("MaximumRadius")
                        .HasColumnType("float");

                    b.Property<double?>("MaximumWeight")
                        .HasColumnType("float");

                    b.Property<string>("PlannerName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PlannerSignature")
                        .HasMaxLength(8128)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PreferredNonRoutineMethod")
                        .HasColumnType("int");

                    b.Property<string>("SecondColorCode")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<Guid?>("SquadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("LiftingPlanId");

                    b.HasIndex("AreaId");

                    b.HasIndex("CraneId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("SquadId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("LiftingPlans");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LiftingPlanEmployee", b =>
                {
                    b.Property<Guid>("LiftingPlanEmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("LiftingPlanId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Role")
                        .HasColumnType("int");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("LiftingPlanEmployeeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("LiftingPlanId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("LiftingPlanEmployees");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LiftingPlanResource", b =>
                {
                    b.Property<Guid>("LiftingPlanResourceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Equipment")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EquipmentType")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("Length")
                        .HasColumnType("float");

                    b.Property<Guid>("LiftingPlanId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Qty")
                        .HasColumnType("int");

                    b.Property<double>("SafeWorkingLoad")
                        .HasColumnType("float");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("LiftingPlanResourceId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LiftingPlanId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("LiftingPlanResources");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LoadCell", b =>
                {
                    b.Property<Guid>("LoadCellId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DataTag")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<string>("Id")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PortNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("LoadCellId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("LoadCells");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Location", b =>
                {
                    b.Property<Guid>("LocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("BusinessDaysFrom")
                        .HasColumnType("int");

                    b.Property<int>("BusinessDaysTo")
                        .HasColumnType("int");

                    b.Property<string>("BusinessHoursFrom")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("BusinessHoursTo")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<bool>("CanCreateOneOffCargo")
                        .HasColumnType("bit");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("MeasurementUnit")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("PlanMode")
                        .HasColumnType("int");

                    b.Property<string>("TimeZoneInfoId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("WasteCarrierCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("LocationId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Locations");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.MobileWell", b =>
                {
                    b.Property<Guid>("MobileWellId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("EndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("MobileId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("WellId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("MobileWellId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("MobileId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("WellId");

                    b.ToTable("MobileWells");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.MovementMatching", b =>
                {
                    b.Property<Guid>("MovementMatchingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CargoDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<int>("Direction")
                        .HasColumnType("int");

                    b.Property<string>("ExceptionReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HireBillingAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HireBillingAssetName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HireClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HireClientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HireRequestCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("HireRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HireUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HireUnitType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HireVendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HireVendorName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<string>("ManifestNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("MatchedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("MatchedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("MovementAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MovementAssetName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("MovementClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MovementClientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MovementUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("MovementVendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MovementVendorName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("OffHiredDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("OnHiredDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("Returned")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("SailingDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("Shipped")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("VoyageName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("MovementMatchingId");

                    b.HasIndex("HireRequestCargoId");

                    b.ToTable("MovementMatchings");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.OffshoreLocation", b =>
                {
                    b.Property<Guid>("OffshoreLocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("ManifestNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("OffshoreLocationId");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("OffshoreLocations");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.PackingUnit", b =>
                {
                    b.Property<Guid>("PackingUnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("PackingUnitId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("PackingUnits");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ParallelActivity", b =>
                {
                    b.Property<Guid>("ParallelActivityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActivityOneId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActivityTwoId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ParallelActivityId");

                    b.HasIndex("ActivityOneId");

                    b.HasIndex("ActivityTwoId");

                    b.ToTable("ParallelActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.PauseReason", b =>
                {
                    b.Property<Guid>("PauseReasonId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("PauseReasonId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("PauseReason");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Pool", b =>
                {
                    b.Property<Guid>("PoolId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("PoolId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Pools");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ReportType", b =>
                {
                    b.Property<Guid>("ReportTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ReportTypeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ReportTypes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequest", b =>
                {
                    b.Property<Guid>("SailingRequestId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ArrivalTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ClientReference")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("ClusterID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("ClusterTime")
                        .HasColumnType("float");

                    b.Property<string>("Comment")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<TimeOnly?>("ETA")
                        .HasColumnType("time");

                    b.Property<TimeOnly?>("ETD")
                        .HasColumnType("time");

                    b.Property<DateOnly?>("EndTime")
                        .HasColumnType("date");

                    b.Property<DateTime?>("FirstInstallationTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InboundVoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsFlexableTiming")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInbound")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInterfield")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOutbound")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LatestArrivalTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("LightDues")
                        .HasColumnType("bit");

                    b.Property<Guid?>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("OutboundVoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Pilot")
                        .HasColumnType("bit");

                    b.Property<string>("Remarks")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("RepeatEveryNumberOfWeeks")
                        .HasColumnType("int");

                    b.Property<DateOnly?>("SeriesEndTime")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("SeriesStartTime")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("StartTime")
                        .HasColumnType("date");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TimeUnit")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VesselId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("WeeklyPattern")
                        .HasMaxLength(56)
                        .HasColumnType("nvarchar(56)");

                    b.Property<bool>("isBulkReq")
                        .HasColumnType("bit");

                    b.Property<bool>("isMailbag")
                        .HasColumnType("bit");

                    b.HasKey("SailingRequestId");

                    b.HasIndex("ClientId");

                    b.HasIndex("ClusterID");

                    b.HasIndex("CreatedById");

                    b.HasIndex("InboundVoyageId");

                    b.HasIndex("LocationId");

                    b.HasIndex("OutboundVoyageId");

                    b.HasIndex("ParentId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VesselId");

                    b.ToTable("SailingRequests");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequestActivity", b =>
                {
                    b.Property<Guid>("SailingRequestActivityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ActivityCategoryTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("DependantActivityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<double>("Hours")
                        .HasColumnType("float");

                    b.Property<bool>("IsInPort")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double?>("Quantity")
                        .HasColumnType("float");

                    b.Property<Guid?>("SailingRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("isDisabled")
                        .HasColumnType("bit");

                    b.HasKey("SailingRequestActivityId");

                    b.HasIndex("ActivityCategoryTypeId");

                    b.HasIndex("AreaId");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DependantActivityId")
                        .IsUnique()
                        .HasFilter("[DependantActivityId] IS NOT NULL");

                    b.HasIndex("SailingRequestId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("SailingRequestActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequestAsset", b =>
                {
                    b.Property<Guid>("SailingRequestAssetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("SailingRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("SailingRequestAssetId");

                    b.HasIndex("AssetId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("SailingRequestId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("SailingRequestAssets");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequestUserComment", b =>
                {
                    b.Property<Guid>("SailingRequestUserCommentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("SailingRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("SailingRequestUserCommentId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("SailingRequestId");

                    b.ToTable("SailingRequestUserComments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Setting", b =>
                {
                    b.Property<Guid>("SettingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AllowPassToInt")
                        .HasColumnType("bit");

                    b.Property<bool>("AllowPassToPass")
                        .HasColumnType("bit");

                    b.Property<string>("Currency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<Guid?>("SettingsDefaultInitialPortId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("SettingId");

                    b.HasIndex("SettingsDefaultInitialPortId");

                    b.ToTable("Settings");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Site", b =>
                {
                    b.Property<Guid>("SiteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("SiteId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Squad", b =>
                {
                    b.Property<Guid>("SquadId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SquadName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("SquadId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Squads");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SquadEmployee", b =>
                {
                    b.Property<Guid>("SquadEmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SquadId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("SquadEmployeeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("SquadId");

                    b.ToTable("SquadEmployees");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TankStatus", b =>
                {
                    b.Property<Guid>("TankStatusId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BulkTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("Cleaned")
                        .HasColumnType("bit");

                    b.Property<string>("Comments")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DischargeDateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsBackload")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LoadDateTime")
                        .HasColumnType("datetime2");

                    b.Property<double?>("QuantityOnBoarding")
                        .HasColumnType("float");

                    b.Property<double?>("QuantityTransferred")
                        .HasColumnType("float");

                    b.Property<string>("RecordingType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<double?>("SpecificGravity")
                        .HasColumnType("float");

                    b.Property<string>("Status")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("StatusDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("TankStatusNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("WellNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("TankStatusId");

                    b.HasIndex("BulkTransactionId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("TankStatuses");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TankType", b =>
                {
                    b.Property<Guid>("TankTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TankTypeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("TankTypes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ToolBoxTalk", b =>
                {
                    b.Property<Guid>("ToolBoxTalkId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AdditionalResourceDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("AdditionalResourceDiscussed")
                        .HasColumnType("bit");

                    b.Property<string>("AnyOtherHazardsComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ControlMeasureDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("DeliveryTipsStepperHit")
                        .HasColumnType("bit");

                    b.Property<bool>("DetailStepperHit")
                        .HasColumnType("bit");

                    b.Property<string>("ExplainInDetailDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ExplainInDetailDiscussed")
                        .HasColumnType("bit");

                    b.Property<bool>("HasCongestedRestrictedSpaceSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasDustSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasElectricitySelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasFatigueSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasGroundConditionsSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasHazardousMaterialsSubstancesSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasHouseKeepingSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasKeyStepsPrepared")
                        .HasColumnType("bit");

                    b.Property<bool>("HasLightingSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasLoadCentreOfGravitySelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasLoadSecuritySelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasManualHandlingSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasNecessaryDoc")
                        .HasColumnType("bit");

                    b.Property<bool>("HasNoiseExposureSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasOverheadObstructionSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasPlantMovementSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasPotentialDroppedObjectsSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasSpillageSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasStruckBySelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasSuspendedLoadsSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasToolBoxTalkLocation")
                        .HasColumnType("bit");

                    b.Property<bool>("HasTrafficManagementSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasTrappingCrushingSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasUseOfSharpToolsSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasWeatherConditionsSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasWorkSiteVisited")
                        .HasColumnType("bit");

                    b.Property<bool>("HasWorkingAtHeightSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HasWorkingNearWaterSelected")
                        .HasColumnType("bit");

                    b.Property<bool>("HazardsStepperHit")
                        .HasColumnType("bit");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEveryoneComfortableToProceed")
                        .HasColumnType("bit");

                    b.Property<Guid?>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OtherHazardsDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OverAllResponsibilityDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("OverAllResponsibilityDiscussed")
                        .HasColumnType("bit");

                    b.Property<bool>("PreparationStepperHit")
                        .HasColumnType("bit");

                    b.Property<string>("ProceduresAndInstructionsDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ProceduresAndInstructionsDiscussed")
                        .HasColumnType("bit");

                    b.Property<string>("QuestionsAskedDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SelectAttendeesStepperHit")
                        .HasColumnType("bit");

                    b.Property<bool>("SignatureStepperHit")
                        .HasColumnType("bit");

                    b.Property<Guid?>("SquadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("SubmittedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TaskRiskAssessmentDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TaskRiskAssessmentDiscussed")
                        .HasColumnType("bit");

                    b.Property<string>("ThirdPartyDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ThirdPartyDiscussed")
                        .HasColumnType("bit");

                    b.Property<int>("ToolBoxTalkStatus")
                        .HasColumnType("int");

                    b.Property<bool>("UnderstandingStepperHit")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VersionNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("WereQuestionsAsked")
                        .HasColumnType("bit");

                    b.Property<string>("WhatControlMeasureDoYouHaveInPlaceComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkSiteOverViewDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("WorkSiteOverViewDiscussed")
                        .HasColumnType("bit");

                    b.HasKey("ToolBoxTalkId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("SquadId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ToolBoxTalks");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ToolBoxTalkEmployee", b =>
                {
                    b.Property<Guid>("ToolBoxTalkEmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Signature")
                        .HasMaxLength(70000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ToolBoxTalkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ToolBoxTalkEmployeeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("ToolBoxTalkId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ToolBoxTalkEmployees");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ToolBoxTalkSite", b =>
                {
                    b.Property<Guid>("ToolBoxTalkSiteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ToolBoxTalkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ToolBoxTalkSiteId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("SiteId");

                    b.HasIndex("ToolBoxTalkId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("ToolBoxTalkSites");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Trailer", b =>
                {
                    b.Property<Guid>("TrailerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAdhoc")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RegistrationNumber")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TrailerId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Trailer");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequest", b =>
                {
                    b.Property<Guid>("TransportRequestId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsComplete")
                        .HasColumnType("bit");

                    b.Property<Guid>("SailingRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SubmittedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TodoSpecialComments")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("VoyageDirection")
                        .HasColumnType("int");

                    b.Property<Guid?>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("TransportRequestId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("SailingRequestId");

                    b.HasIndex("SubmittedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("TransportRequests");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestAttachment", b =>
                {
                    b.Property<Guid>("TransportRequestAttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("DocumentSize")
                        .HasColumnType("float");

                    b.Property<Guid>("TransportRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TransportRequestAttachmentId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TransportRequestId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("TransportRequestAttachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargo", b =>
                {
                    b.Property<Guid>("TransportRequestBulkCargoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BulkTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CancellationReason")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Comment")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("FromAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("FromLocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsWaste")
                        .HasColumnType("bit");

                    b.Property<bool>("LimitedQuantity")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("Quantity")
                        .HasColumnType("float");

                    b.Property<double>("Sg")
                        .HasColumnType("float");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("StatusUpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("StatusUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("SubmittedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("SubmittedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ToAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ToLocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("TodoComplete")
                        .HasColumnType("bit");

                    b.Property<bool>("TodoStatus")
                        .HasColumnType("bit");

                    b.Property<Guid?>("TransportRequestBulkCargoDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TransportRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("TransportRequestBulkCargoId");

                    b.HasIndex("BulkTypeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FromAssetId");

                    b.HasIndex("FromLocationId");

                    b.HasIndex("StatusUpdatedById");

                    b.HasIndex("SubmittedById");

                    b.HasIndex("ToAssetId");

                    b.HasIndex("ToLocationId");

                    b.HasIndex("TransportRequestBulkCargoDangerousGoodId");

                    b.HasIndex("TransportRequestId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.ToTable("TransportRequestBulkCargos");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargoAttachment", b =>
                {
                    b.Property<Guid>("TransportRequestBulkCargoAttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TransportRequestBulkCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TransportRequestBulkCargoAttachmentId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TransportRequestBulkCargoId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("TransportRequestBulkCargoAttachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargoDangerousGood", b =>
                {
                    b.Property<Guid>("TransportRequestBulkCargoDangerousGoodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool?>("LtdQty")
                        .HasColumnType("bit");

                    b.Property<bool?>("MarinePollutant")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TransportRequestBulkCargoDangerousGoodId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DangerousGoodId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("TransportRequestBulkCargoDangerousGoods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargoSnapshot", b =>
                {
                    b.Property<Guid>("TransportRequestBulkCargoSnapshotId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("TransportRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Version")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("TransportRequestBulkCargoSnapshotId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TransportRequestId");

                    b.ToTable("TransportRequestBulkCargoSnapshots");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargo", b =>
                {
                    b.Property<Guid>("TransportRequestCargoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ApprovedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("AutoApprove")
                        .HasColumnType("bit");

                    b.Property<string>("CancellationReason")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CargoDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CargoExpectedDeliveryTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("CargoIsDeckCargo")
                        .HasColumnType("bit");

                    b.Property<double>("CargoLength")
                        .HasColumnType("float");

                    b.Property<double>("CargoWidth")
                        .HasColumnType("float");

                    b.Property<int?>("ChangeReason")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CollectDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Comments")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<double?>("Diameter")
                        .HasColumnType("float");

                    b.Property<bool>("Empty")
                        .HasColumnType("bit");

                    b.Property<double?>("EstimatedCargoWeight")
                        .HasColumnType("float");

                    b.Property<DateTime?>("ExpectedDeliveryTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("FromAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("FromLocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPriority")
                        .HasColumnType("bit");

                    b.Property<bool>("IsWaste")
                        .HasColumnType("bit");

                    b.Property<int?>("LoadingInstructions")
                        .HasColumnType("int");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("MovedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("MovedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("NumberOfLifts")
                        .HasColumnType("int");

                    b.Property<string>("OneOffCcuId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PoNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("PriorityOrder")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Requestor")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SealNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("SpecialCargoProcessedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("SpecialCargoProcessedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("StatusUpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("StatusUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("SubmittedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("SubmittedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ToAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("ToDoSpecialCargoComplete")
                        .HasColumnType("bit");

                    b.Property<bool?>("ToDoTransferComplete")
                        .HasColumnType("bit");

                    b.Property<bool?>("ToDoWasteComplete")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ToLocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransferProcessedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("TransferProcessedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TransportAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TransportRequestCargoBundlingId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("TransportRequestCargoCategory")
                        .HasColumnType("int");

                    b.Property<Guid>("TransportRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransportRequestMovedFromId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("TransportRequirement")
                        .HasColumnType("int");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VendorWarehouseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ViaVendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ViaVendorWarehouseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WasteProcessedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("WasteProcessedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TransportRequestCargoId");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("CargoId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FromAssetId");

                    b.HasIndex("FromLocationId");

                    b.HasIndex("LocationId");

                    b.HasIndex("MovedById");

                    b.HasIndex("SpecialCargoProcessedById");

                    b.HasIndex("StatusUpdatedById");

                    b.HasIndex("SubmittedById");

                    b.HasIndex("ToAssetId");

                    b.HasIndex("ToLocationId");

                    b.HasIndex("TransferProcessedById");

                    b.HasIndex("TransportRequestCargoBundlingId");

                    b.HasIndex("TransportRequestId");

                    b.HasIndex("TransportRequestMovedFromId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorWarehouseId");

                    b.HasIndex("ViaVendorId");

                    b.HasIndex("ViaVendorWarehouseId");

                    b.HasIndex("WasteProcessedById");

                    b.ToTable("TransportRequestCargos");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargoAttachment", b =>
                {
                    b.Property<Guid>("TransportRequestCargoAttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TransportRequestCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("TransportRequestCargoAttachmentId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TransportRequestCargoId");

                    b.ToTable("TransportRequestCargoAttachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargoBundling", b =>
                {
                    b.Property<Guid>("TransportRequestCargoBundlingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DeliveryType")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("NumberOfLiftsRequired")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfSlingsRequired")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("QuantityPerBundle")
                        .HasColumnType("int");

                    b.Property<int>("SlingType")
                        .HasColumnType("int");

                    b.Property<bool>("SlingsRequiredFromControlUnion")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TransportRequestCargoBundlingId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("TransportRequestCargoBundlings");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargoDangerousGood", b =>
                {
                    b.Property<Guid>("TransportRequestCargoDangerousGoodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool?>("LtdQty")
                        .HasColumnType("bit");

                    b.Property<bool?>("MarinePollutant")
                        .HasColumnType("bit");

                    b.Property<Guid>("TransportRequestCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TransportRequestCargoDangerousGoodId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DangerousGoodId");

                    b.HasIndex("TransportRequestCargoId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("TransportRequestCargoDangerousGoods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargoSnapshot", b =>
                {
                    b.Property<Guid>("TransportRequestCargoSnapshotId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("TransportRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Version")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("TransportRequestCargoSnapshotId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TransportRequestId");

                    b.ToTable("TransportRequestCargoSnapshots");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestMaterialDetail", b =>
                {
                    b.Property<Guid>("TransportRequestMaterialDetailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CancellationReason")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("ChangeReason")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<string>("CommodityCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CountryOfOrigin")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CustomsDocumentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomsDocumentNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("CustomsEntryType")
                        .HasColumnType("int");

                    b.Property<int?>("CustomsStatus")
                        .HasColumnType("int");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("EWC")
                        .HasColumnType("int");

                    b.Property<bool>("Emballage")
                        .HasColumnType("bit");

                    b.Property<double>("EstimatedWeight")
                        .HasColumnType("float");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<string>("ManifestNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("MaxStockQuantity")
                        .HasColumnType("int");

                    b.Property<string>("MaxStockValue")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("OffshoreInstallation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("OffshoreInstallationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("PackingGroup")
                        .HasColumnType("int");

                    b.Property<Guid?>("PackingUnitId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ParentCargoItemCategory")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ParentCargoItemStatus")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PickupLocation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PoNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ProperShippingName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int?>("RejectedReason")
                        .HasColumnType("int");

                    b.Property<string>("Requestor")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StockMaterialReturnedAs")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("SubmittedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("SubmittedDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("SupplyQuantity")
                        .HasColumnType("float");

                    b.Property<int?>("SupplyUnitType")
                        .HasColumnType("int");

                    b.Property<bool?>("ToDoIMDGComplete")
                        .HasColumnType("bit");

                    b.Property<Guid?>("TransportRequestBulkCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransportRequestCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TransportRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransportRequestMaterialDetailDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Waste")
                        .HasColumnType("bit");

                    b.Property<string>("WasteDescription")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("WorkOrder")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("TransportRequestMaterialDetailId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("OffshoreInstallationId");

                    b.HasIndex("PackingUnitId");

                    b.HasIndex("SubmittedById");

                    b.HasIndex("TransportRequestBulkCargoId");

                    b.HasIndex("TransportRequestCargoId");

                    b.HasIndex("TransportRequestId");

                    b.HasIndex("TransportRequestMaterialDetailDangerousGoodId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.ToTable("TransportRequestMaterialDetails");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestMaterialDetailDangerousGood", b =>
                {
                    b.Property<Guid>("TransportRequestMaterialDetailDangerousGoodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool?>("TodoApprovalStatus")
                        .HasColumnType("bit");

                    b.Property<int?>("TodoRejectedReason")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransportRequestBulkCargoDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TransportRequestCargoDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TransportRequestMaterialDetailDangerousGoodId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TransportRequestBulkCargoDangerousGoodId");

                    b.HasIndex("TransportRequestCargoDangerousGoodId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("TransportRequestMaterialDetailDangerousGoods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestMaterialDetailSnapshot", b =>
                {
                    b.Property<Guid>("TransportRequestMaterialDetailSnapshotId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("TransportRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Version")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("TransportRequestMaterialDetailSnapshotId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TransportRequestId");

                    b.ToTable("TransportRequestMaterialDetailSnapshots");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestmaterialDetailAttachment", b =>
                {
                    b.Property<Guid>("TransportRequestmaterialDetailAttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("TransportRequestmaterialDetailId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("TransportRequestmaterialDetailAttachmentId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TransportRequestmaterialDetailId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("TransportRequestmaterialDetailAttachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Unit", b =>
                {
                    b.Property<Guid>("UnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("UnitId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Units");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.User", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("Disabled")
                        .HasColumnType("bit");

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Firstname")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Lastname")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.HasKey("UserId");

                    b.HasIndex("ClientId");

                    b.HasIndex("EmailAddress");

                    b.HasIndex("Firstname");

                    b.HasIndex("Lastname");

                    b.HasIndex("LocationId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Vehicle", b =>
                {
                    b.Property<Guid>("VehicleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAdhoc")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RegistrationNumber")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("VehicleId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Vehicles");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Vendor", b =>
                {
                    b.Property<Guid>("VendorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Address")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("City")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Country")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPeterson")
                        .HasColumnType("bit");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PostCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VendorName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("WarehouseCount")
                        .HasColumnType("int");

                    b.HasKey("VendorId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("Vendors");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VendorWarehouse", b =>
                {
                    b.Property<Guid>("VendorWarehouseId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Address")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("City")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Country")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("DistrictId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PostCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VendorWarehouseId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DistrictId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.ToTable("VendorWarehouses");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Vessel", b =>
                {
                    b.Property<Guid>("VesselId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("Construction")
                        .HasColumnType("datetime2");

                    b.Property<string>("Country")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("DeadWeightKg")
                        .HasColumnType("float");

                    b.Property<string>("DeadWeightUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("DeckCapacity")
                        .HasColumnType("float");

                    b.Property<string>("DeckCapacityUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("DeckLengthMm")
                        .HasColumnType("float");

                    b.Property<string>("DeckLengthUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("DeckWidthMm")
                        .HasColumnType("float");

                    b.Property<string>("DeckWidthUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<int?>("DpClass")
                        .HasColumnType("int");

                    b.Property<double>("Draft")
                        .HasColumnType("float");

                    b.Property<string>("DraftUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("Eori")
                        .HasColumnType("int");

                    b.Property<bool>("Errv")
                        .HasColumnType("bit");

                    b.Property<int?>("FireFightClass")
                        .HasColumnType("int");

                    b.Property<double>("GrossTonnage")
                        .HasColumnType("float");

                    b.Property<string>("GrossTonnageUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Imo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("Inactive")
                        .HasColumnType("bit");

                    b.Property<double>("LengthMm")
                        .HasColumnType("float");

                    b.Property<string>("LengthUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Mmsi")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("NetTonnage")
                        .HasColumnType("float");

                    b.Property<string>("NetTonnageUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("RoundedSafeHaven")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VesselOwner")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("VesselPictureId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("WidthMm")
                        .HasColumnType("float");

                    b.Property<string>("WidthUnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("VesselId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Vessels");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VesselActivity", b =>
                {
                    b.Property<Guid>("VesselActivityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("ActivityAssetSequenceNotOk")
                        .HasColumnType("bit");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("ActivityOperatorAndAssetNotOk")
                        .HasColumnType("bit");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AssetWasOwnedByOperatorAtActivityTime")
                        .HasColumnType("bit");

                    b.Property<Guid?>("BilledAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<double?>("DeckSpaceUsed")
                        .HasColumnType("float");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<double?>("Duration")
                        .HasColumnType("float");

                    b.Property<DateTime>("EndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("InvalidErrors")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("OverlapsOtherVoyagesForVessel")
                        .HasColumnType("bit");

                    b.Property<bool>("ParallelActivityNotOk")
                        .HasColumnType("bit");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("TimeGapNotOk")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VesselActivityId");

                    b.HasIndex("ActivityId");

                    b.HasIndex("AssetId");

                    b.HasIndex("BilledAssetId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VesselActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VesselTank", b =>
                {
                    b.Property<Guid>("VesselTankId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BulkTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Cleaned")
                        .HasColumnType("bit");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("DayRatePrice")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("Quantity")
                        .HasColumnType("float");

                    b.Property<DateTime?>("TankStatusChangeDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("TankTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VesselId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VesselTankId");

                    b.HasIndex("BulkTypeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("TankTypeId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VesselId");

                    b.ToTable("VesselTanks");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Voyage", b =>
                {
                    b.Property<Guid>("VoyageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ArchivedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("AreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<Guid?>("CompletedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("DeckPercentageUsedIn")
                        .HasColumnType("float");

                    b.Property<double>("DeckPercentageUsedOut")
                        .HasColumnType("float");

                    b.Property<double?>("DeckUtilisationPercentage")
                        .HasColumnType("float");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DepartureEmailLastSent")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DiscrepancyEmailLastSent")
                        .HasColumnType("datetime2");

                    b.Property<double>("DistanceSailed")
                        .HasColumnType("float");

                    b.Property<Guid?>("FinalAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("HasMovedBillingPeriod")
                        .HasColumnType("bit");

                    b.Property<bool>("HasSentDepartureEmail")
                        .HasColumnType("bit");

                    b.Property<bool>("HasSentDiscrepancyReport")
                        .HasColumnType("bit");

                    b.Property<Guid?>("InitialAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsAnchorHandlingVessel")
                        .HasColumnType("bit");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("bit");

                    b.Property<bool>("IsBallasClearDeckVoyage")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVoyageLocked")
                        .HasColumnType("bit");

                    b.Property<Guid?>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ReleasedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ReleasedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ReportingDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("SailingDischargeDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SpecialNotes")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<double>("TotalMileage")
                        .HasColumnType("float");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VesselId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("VesselIsRightToLeft")
                        .HasColumnType("bit");

                    b.Property<int>("VoyageDirection")
                        .HasColumnType("int");

                    b.Property<DateTime?>("VoyageEndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("VoyageIsNotLoadedByUs")
                        .HasColumnType("bit");

                    b.Property<bool>("VoyageNotValid")
                        .HasColumnType("bit");

                    b.Property<string>("VoyageNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("VoyageStartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("VoyageStatus")
                        .HasColumnType("int");

                    b.Property<int>("VoyageType")
                        .HasColumnType("int");

                    b.HasKey("VoyageId");

                    b.HasIndex("AreaId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CompletedByUserId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FinalAssetId");

                    b.HasIndex("InitialAssetId");

                    b.HasIndex("LocationId");

                    b.HasIndex("ReleasedById");

                    b.HasIndex("SiteId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VesselId");

                    b.ToTable("Voyages");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageAttachment", b =>
                {
                    b.Property<Guid>("VoyageAttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AttachmentType")
                        .HasColumnType("int");

                    b.Property<Guid>("BlobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("FileName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double>("FileSize")
                        .HasColumnType("float");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageAttachmentId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageAttachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageBillingPeriod", b =>
                {
                    b.Property<Guid>("VoyageBillingPeriodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("AverageCharterRate")
                        .HasColumnType("float");

                    b.Property<Guid>("BillingPeriodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageBillingPeriodId");

                    b.HasIndex("BillingPeriodId");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageBillingPeriods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageBulk", b =>
                {
                    b.Property<Guid>("VoyageBulkId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BulkTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateLoaded")
                        .HasColumnType("datetime2");

                    b.Property<double>("Price")
                        .HasColumnType("float");

                    b.Property<double>("QuantityLeftover")
                        .HasColumnType("float");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageBulkId");

                    b.HasIndex("BulkTypeId");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageBulks");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageBulkQuantities", b =>
                {
                    b.Property<Guid>("VoyageBulkQuantitiesId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BulkTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("EndQuantity")
                        .HasColumnType("float");

                    b.Property<double>("StartQuantity")
                        .HasColumnType("float");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageBulkQuantitiesId");

                    b.HasIndex("BulkTypeId");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageBulkQuantities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargo", b =>
                {
                    b.Property<Guid>("VoyageCargoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("ActualWeight")
                        .HasColumnType("float");

                    b.Property<DateTime?>("ArrivalTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("BumpedOrCancelledOrRTDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CargoBackloadDischargeDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CargoBackloadStatus")
                        .HasColumnType("int");

                    b.Property<string>("CargoDescription")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("CargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("CargoLengthMm")
                        .HasColumnType("float");

                    b.Property<DateTime?>("CargoNextTestDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CargoUnitType")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<double?>("CargoWeightKg")
                        .HasColumnType("float");

                    b.Property<double?>("CargoWidthMm")
                        .HasColumnType("float");

                    b.Property<string>("CcuId")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateOnly?>("CollectDate")
                        .HasColumnType("date");

                    b.Property<TimeOnly?>("CollectTime")
                        .HasColumnType("time");

                    b.Property<string>("Comments")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("CompletedLift")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("CopiedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomReferenceNo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("CustomStatus")
                        .HasColumnType("int");

                    b.Property<int?>("CustomsApprovedToLoad")
                        .HasColumnType("int");

                    b.Property<int?>("CustomsEntryType")
                        .HasColumnType("int");

                    b.Property<int?>("DeckLocation")
                        .HasColumnType("int");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DispatchTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DistrictId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsBumped")
                        .HasColumnType("bit");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsCopied")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExtra")
                        .HasColumnType("bit");

                    b.Property<bool>("IsHighPriority")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMoved")
                        .HasColumnType("bit");

                    b.Property<int?>("Level")
                        .HasColumnType("int");

                    b.Property<Guid?>("LiftedAtAreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("LiftingTag")
                        .HasColumnType("int");

                    b.Property<DateTime?>("MovedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("NumberOfLifts")
                        .HasColumnType("int");

                    b.Property<double?>("OriginalActualWeight")
                        .HasColumnType("float");

                    b.Property<int?>("OriginalQuantity")
                        .HasColumnType("int");

                    b.Property<string>("Owner")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("PassedInspection")
                        .HasColumnType("bit");

                    b.Property<string>("PickupAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("PriorityOrder")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("RowNumber")
                        .HasColumnType("int");

                    b.Property<int?>("RtRob")
                        .HasColumnType("int");

                    b.Property<string>("SepaNumber")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<Guid?>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("TrailerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TrailerNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("TransportAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("TransportName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("TransportPhoneNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("TransportRequest")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransportRequestCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("TransportStatus")
                        .HasColumnType("int");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VendorWarehouseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ViaVendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ViaVendorWarehouseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VoyageCargoLoadId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VoyageCargoParentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WeightCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("XVesselPosition")
                        .HasColumnType("float");

                    b.Property<double?>("YVesselPosition")
                        .HasColumnType("float");

                    b.HasKey("VoyageCargoId");

                    b.HasIndex("AssetId");

                    b.HasIndex("CargoId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DistrictId");

                    b.HasIndex("LiftedAtAreaId");

                    b.HasIndex("SiteId");

                    b.HasIndex("TrailerId");

                    b.HasIndex("TransportRequestCargoId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorWarehouseId");

                    b.HasIndex("ViaVendorId");

                    b.HasIndex("ViaVendorWarehouseId");

                    b.HasIndex("VoyageCargoLoadId");

                    b.HasIndex("VoyageCargoParentId");

                    b.HasIndex("VoyageId");

                    b.HasIndex("WeightCategoryId");

                    b.HasIndex("RowNumber", "VoyageId")
                        .HasDatabaseName("IX_VoyageCargoes_RowNumber_VoyageId");

                    b.ToTable("VoyageCargos");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoBulk", b =>
                {
                    b.Property<Guid>("VoyageCargoBulkId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BulkTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CustomStatus")
                        .HasColumnType("int");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<double?>("DeliveredQuantity")
                        .HasColumnType("float");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<double?>("PPGSG")
                        .HasColumnType("float");

                    b.Property<double?>("Quantity")
                        .HasColumnType("float");

                    b.Property<int>("RowNumber")
                        .HasColumnType("int");

                    b.Property<int?>("RtRob")
                        .HasColumnType("int");

                    b.Property<Guid?>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransportRequestBulkCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TypeOfBulk")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UnitName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VoyageCargoBulkDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WeightCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCargoBulkId");

                    b.HasIndex("AssetId");

                    b.HasIndex("BulkTypeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("SiteId");

                    b.HasIndex("TransportRequestBulkCargoId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.HasIndex("VoyageCargoBulkDangerousGoodId");

                    b.HasIndex("VoyageId");

                    b.HasIndex("WeightCategoryId");

                    b.HasIndex("RowNumber", "VoyageId")
                        .HasDatabaseName("IX_VoyageCargoBulks_RowNumber_VoyageId");

                    b.ToTable("VoyageCargoBulks");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoBulkDangerousGood", b =>
                {
                    b.Property<Guid>("VoyageCargoBulkDangerousGoodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool?>("LtdQty")
                        .HasColumnType("bit");

                    b.Property<bool?>("MarinePollutant")
                        .HasColumnType("bit");

                    b.Property<Guid?>("TransportRequestBulkCargoDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("VoyageCargoBulkDangerousGoodId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DangerousGoodId");

                    b.HasIndex("TransportRequestBulkCargoDangerousGoodId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("VoyageCargoBulkDangerousGood");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoBulkSnapshot", b =>
                {
                    b.Property<Guid>("VoyageCargoBulkSnapshotId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Version")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCargoBulkSnapshotId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageCargoBulkSnapshots");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoDangerousGood", b =>
                {
                    b.Property<Guid>("VoyageCargoDangerousGoodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("LtdQty")
                        .HasColumnType("bit");

                    b.Property<bool>("MarinePollutant")
                        .HasColumnType("bit");

                    b.Property<Guid?>("TransportRequestCargoDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VoyageCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCargoDangerousGoodId");

                    b.HasIndex("DangerousGoodId");

                    b.HasIndex("TransportRequestCargoDangerousGoodId");

                    b.HasIndex("VoyageCargoId");

                    b.ToTable("VoyageCargoDangerousGoods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoInspection", b =>
                {
                    b.Property<Guid>("VoyageCargoInspectionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("FailReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("RecordedTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("RectifiedAtHoist")
                        .HasColumnType("bit");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TrailerNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VoyageInspectionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCargoInspectionId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageCargoId")
                        .IsUnique();

                    b.HasIndex("VoyageInspectionId");

                    b.ToTable("VoyageCargoInspections");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoInspectionAttachment", b =>
                {
                    b.Property<Guid>("VoyageCargoInspectionAttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BlobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("FileName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VoyageCargoInspectionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCargoInspectionAttachmentId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageCargoInspectionId");

                    b.ToTable("VoyageCargoInspectionAttachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoLift", b =>
                {
                    b.Property<Guid>("VoyageCargoLiftId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("CapturedWeightKg")
                        .HasColumnType("float");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("Inactive")
                        .HasColumnType("bit");

                    b.Property<Guid>("LoadCellId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCargoLiftId");

                    b.HasIndex("AreaId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LoadCellId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageCargoId");

                    b.ToTable("VoyageCargoLifts");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoLoad", b =>
                {
                    b.Property<Guid>("VoyageCargoLoadId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DispatchDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DriverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Extender")
                        .HasColumnType("bit");

                    b.Property<int?>("LoadStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<int>("LoadType")
                        .HasColumnType("int");

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("PaperworkCompleteDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("TrailerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VehicleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("WideLoad")
                        .HasColumnType("bit");

                    b.HasKey("VoyageCargoLoadId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DriverId");

                    b.HasIndex("LocationId");

                    b.HasIndex("TrailerId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VehicleId");

                    b.ToTable("VoyageCargoLoads");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoSailingRequestActivity", b =>
                {
                    b.Property<Guid>("VoyageCargoSailingRequestActivityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("SailingRequestActivityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCargoSailingRequestActivityId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("SailingRequestActivityId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageCargoId");

                    b.ToTable("VoyageCargoSailingRequestActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoSnapshot", b =>
                {
                    b.Property<Guid>("VoyageCargoSnapshotId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Version")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCargoSnapshotId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageCargoSnapshots");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageComment", b =>
                {
                    b.Property<Guid>("VoyageCommentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageCommentId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageComments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageEvent", b =>
                {
                    b.Property<Guid>("VoyageEventId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("EventType")
                        .HasColumnType("int");

                    b.Property<double>("RevisionNumber")
                        .HasColumnType("float");

                    b.Property<string>("Title")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageEventId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageEvents");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageInspection", b =>
                {
                    b.Property<Guid>("VoyageInspectionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageInspectionId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId")
                        .IsUnique();

                    b.ToTable("VoyageInspections");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageLiftingJob", b =>
                {
                    b.Property<Guid>("VoyageLiftingJobId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("PauseDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PauseDetail")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("PauseReasonId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageLiftingJobId");

                    b.HasIndex("AreaId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("PauseReasonId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageLiftingJob");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageMaterialDetail", b =>
                {
                    b.Property<Guid>("VoyageMaterialDetailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("COO")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Category")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Class")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateOnly?>("CollectDate")
                        .HasColumnType("date");

                    b.Property<TimeOnly?>("CollectTime")
                        .HasColumnType("time");

                    b.Property<string>("Comments")
                        .HasMaxLength(1023)
                        .HasColumnType("nvarchar(1023)");

                    b.Property<string>("CommodityCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("CustomStatus")
                        .HasColumnType("int");

                    b.Property<int?>("CustomsEntryType")
                        .HasColumnType("int");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("DestinationLocation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("DocumentNo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("IMOCode")
                        .HasColumnType("int");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<string>("JobCardNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("LtdQuantity")
                        .HasColumnType("bit");

                    b.Property<string>("MIVMMT")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ManifestNo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("MarinePollutant")
                        .HasColumnType("bit");

                    b.Property<string>("MaterialDescription")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PONo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("POTransport")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("PackingGroup")
                        .HasColumnType("int");

                    b.Property<Guid?>("PackingUnitId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Phone")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ProperShippingName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Requester")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("RowNumber")
                        .HasColumnType("int");

                    b.Property<string>("SerialNo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("SubClass")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("TransportRequest")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransportRequestMaterialDetailId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VoyageCargoBulkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VoyageCargoId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VoyageMaterialDetailDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("WHSStatus")
                        .HasColumnType("int");

                    b.Property<double>("WeightKg")
                        .HasColumnType("float");

                    b.Property<string>("WhsLocation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("WorkOrderNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("VoyageMaterialDetailId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("PackingUnitId");

                    b.HasIndex("TransportRequestMaterialDetailId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VendorId");

                    b.HasIndex("VoyageCargoBulkId");

                    b.HasIndex("VoyageCargoId");

                    b.HasIndex("VoyageId");

                    b.HasIndex("VoyageMaterialDetailDangerousGoodId");

                    b.HasIndex("RowNumber", "VoyageId")
                        .HasDatabaseName("IX_VoyageMaterialDetails_RowNumber_VoyageId");

                    b.ToTable("VoyageMaterialDetails");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageMaterialDetailDangerousGood", b =>
                {
                    b.Property<Guid>("VoyageMaterialDetailDangerousGoodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("VoyageBulkCargoDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VoyageCargoDangerousGoodId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageMaterialDetailDangerousGoodId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageBulkCargoDangerousGoodId");

                    b.HasIndex("VoyageCargoDangerousGoodId");

                    b.ToTable("VoyageMaterialDetailDangerousGoods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageMaterialDetailSnapshot", b =>
                {
                    b.Property<Guid>("VoyageMaterialDetailSnapshotId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .HasMaxLength(4096)
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Version")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageMaterialDetailSnapshotId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageMaterialDetailSnapshots");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyagePlanningDetail", b =>
                {
                    b.Property<Guid>("VoyagePlanningDetailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ATA")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ATD")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("AreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ETA")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ETD")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsScheduled")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("Time")
                        .HasColumnType("datetime2");

                    b.Property<string>("TuNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("TuType")
                        .HasColumnType("int");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyagePlanningDetailId");

                    b.HasIndex("AreaId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyagePlanningDetails");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageReservedArea", b =>
                {
                    b.Property<Guid>("VoyageReservedAreaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DeckLocation")
                        .HasColumnType("int");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<double>("LengthMm")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("WidthMm")
                        .HasColumnType("float");

                    b.HasKey("VoyageReservedAreaId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageReservedAreas");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageSharer", b =>
                {
                    b.Property<Guid>("VoyageSharerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageSharerId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageSharer");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageSpecialNote", b =>
                {
                    b.Property<Guid>("VoyageSpecialNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("VoyageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("VoyageSpecialNoteId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("VoyageId");

                    b.ToTable("VoyageSpecialNotes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.WeightCategory", b =>
                {
                    b.Property<Guid>("WeightCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<Guid>("LocationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("UpdatedById")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("WeightType")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("WeightCategoryId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("WeightCategories");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Activity", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ActivityCategory", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ActivityCategoryType", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.ActivityCategory", "ActivityCategory")
                        .WithMany("ActivityCategoryTypes")
                        .HasForeignKey("ActivityCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("ActivityCategory");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Area", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Site", "Site")
                        .WithMany("Areas")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Site");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.AreaBlockingActivity", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Area", "Area")
                        .WithMany("AreaBlockingActivities")
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.BlockingActivity", "BlockingActivity")
                        .WithMany()
                        .HasForeignKey("BlockingActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("Area");

                    b.Navigation("BlockingActivity");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Asset", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.AssetLocation", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany("AssetLocations")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany("AssetLocations")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("Asset");

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.AuditLog", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BillingPeriodDocument", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BillingPeriod", "BillingPeriod")
                        .WithMany()
                        .HasForeignKey("BillingPeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BillingPeriod");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BlockingActivity", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BulkRequest", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "BilledAsset")
                        .WithMany()
                        .HasForeignKey("BilledAssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.BulkType", "BulkType")
                        .WithMany()
                        .HasForeignKey("BulkTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("BulkRequests")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BilledAsset");

                    b.Navigation("BulkType");

                    b.Navigation("Client");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BulkTransaction", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId");

                    b.HasOne("Lighthouse.Model.Entity.BulkType", "BulkType")
                        .WithMany()
                        .HasForeignKey("BulkTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TankType", "TankType")
                        .WithMany()
                        .HasForeignKey("TankTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("BulkTransactions")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("BulkType");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("TankType");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BulkType", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Cargo", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.CargoDescription", "CargoDescription")
                        .WithMany()
                        .HasForeignKey("CargoDescriptionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.CargoFamily", "CargoFamily")
                        .WithMany()
                        .HasForeignKey("FamilyId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany("Cargoes")
                        .HasForeignKey("LocationId");

                    b.HasOne("Lighthouse.Model.Entity.Pool", "Pool")
                        .WithMany()
                        .HasForeignKey("PoolId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.CargoSize", "CargoSize")
                        .WithMany()
                        .HasForeignKey("SizeId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.CargoType", "CargoType")
                        .WithMany()
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId");

                    b.Navigation("CargoDescription");

                    b.Navigation("CargoFamily");

                    b.Navigation("CargoSize");

                    b.Navigation("CargoType");

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("Pool");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoCertificate", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Cargo", "Cargo")
                        .WithMany("CargoCertificates")
                        .HasForeignKey("CargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Cargo");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoDescription", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoEvent", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Cargo", "Cargo")
                        .WithMany()
                        .HasForeignKey("CargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId");

                    b.Navigation("Cargo");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoFamily", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoSize", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.CargoFamily", "CargoFamily")
                        .WithMany("CargoSizes")
                        .HasForeignKey("CargoFamilyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CargoFamily");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoType", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.CargoFamily", "CargoFamily")
                        .WithMany("CargoTypes")
                        .HasForeignKey("CargoFamilyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CargoFamily");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Client", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientAsset", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany("ClientAssets")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany("ClientAssets")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientBillingPeriodTimeAllocation", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BillingPeriod", "BillingPeriod")
                        .WithMany()
                        .HasForeignKey("BillingPeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BillingPeriod");

                    b.Navigation("Client");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientLocation", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany("ClientLocations")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany("ClientLocations")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientNameHistory", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany("ClientNameHistory")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClientReportType", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany("ClientReportTypes")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.ReportType", "ReportType")
                        .WithMany()
                        .HasForeignKey("ReportTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ReportType");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ClusterHistory", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "ClusterChild")
                        .WithMany()
                        .HasForeignKey("ClusterChildId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "ClusterHead")
                        .WithMany("ClusterHistory")
                        .HasForeignKey("ClusterHeadId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("ClusterChild");

                    b.Navigation("ClusterHead");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CommentReadByUser", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "ReaderUser")
                        .WithMany()
                        .HasForeignKey("ReaderUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.SailingRequestUserComment", "SailingRequestUserComment")
                        .WithMany("CommentReadByUsers")
                        .HasForeignKey("SailingRequestUserCommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ReaderUser");

                    b.Navigation("SailingRequestUserComment");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Crane", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.DangerousGood", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.DangerousGoodLocation", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.DangerousGood", "DangerousGood")
                        .WithMany("DangerousGoodLocations")
                        .HasForeignKey("DangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany("DangerousGoodLocations")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("DangerousGood");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.DeckUsage", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("DeckUsages")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("Client");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Distance", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "BaseAsset")
                        .WithMany()
                        .HasForeignKey("BaseAssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "ToAsset")
                        .WithMany()
                        .HasForeignKey("ToAssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("BaseAsset");

                    b.Navigation("CreatedBy");

                    b.Navigation("ToAsset");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.District", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany("Districts")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Driver", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Trailer", "Trailer")
                        .WithMany()
                        .HasForeignKey("TrailerId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.VendorWarehouse", "VendorWarehouse")
                        .WithMany()
                        .HasForeignKey("VendorWarehouseId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("Trailer");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vehicle");

                    b.Navigation("Vendor");

                    b.Navigation("VendorWarehouse");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Employee", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireRequest", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.CargoDescription", "CargoDescription")
                        .WithMany()
                        .HasForeignKey("CargoDescriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Asset");

                    b.Navigation("CargoDescription");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireRequestCargo", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "BillingAsset")
                        .WithMany()
                        .HasForeignKey("BillingAssetId");

                    b.HasOne("Lighthouse.Model.Entity.Cargo", "Cargo")
                        .WithMany()
                        .HasForeignKey("CargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "ContainerPool")
                        .WithMany()
                        .HasForeignKey("ContainerPoolId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.HireRequest", "HireRequest")
                        .WithMany("Cargoes")
                        .HasForeignKey("HireRequestId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("BillingAsset");

                    b.Navigation("Cargo");

                    b.Navigation("Client");

                    b.Navigation("ContainerPool");

                    b.Navigation("CreatedBy");

                    b.Navigation("HireRequest");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireRequestCargoEvent", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.HireRequestCargo", "HireRequestCargo")
                        .WithMany()
                        .HasForeignKey("HireRequestCargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("HireRequestCargo");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireStatement", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Vessel", "Vessel")
                        .WithMany("HireStatements")
                        .HasForeignKey("VesselId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vessel");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireStatementBulk", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BulkType", "BulkType")
                        .WithMany()
                        .HasForeignKey("BulkTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.HireStatement", null)
                        .WithMany("HireStatementBulks")
                        .HasForeignKey("HireStatementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("BulkType");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LiftingPlan", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Crane", "Crane")
                        .WithMany()
                        .HasForeignKey("CraneId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Squad", "Squad")
                        .WithMany()
                        .HasForeignKey("SquadId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("LiftingPlans")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Area");

                    b.Navigation("Crane");

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("Squad");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LiftingPlanEmployee", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Employee", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.LiftingPlan", "LiftingPlan")
                        .WithMany("LiftingPlanEmployees")
                        .HasForeignKey("LiftingPlanId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Employee");

                    b.Navigation("LiftingPlan");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LiftingPlanResource", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.LiftingPlan", "LiftingPlan")
                        .WithMany("LiftingPlanResources")
                        .HasForeignKey("LiftingPlanId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("LiftingPlan");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LoadCell", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Location", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.MobileWell", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "Mobile")
                        .WithMany()
                        .HasForeignKey("MobileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "Well")
                        .WithMany()
                        .HasForeignKey("WellId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Mobile");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Well");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.MovementMatching", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.HireRequestCargo", "HireRequestCargo")
                        .WithMany()
                        .HasForeignKey("HireRequestCargoId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("HireRequestCargo");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.OffshoreLocation", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany("VoyageOffshoreLocations")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("OffshoreLocations")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.PackingUnit", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ParallelActivity", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Activity", "ActivityOne")
                        .WithMany("ParallelActivitiesOne")
                        .HasForeignKey("ActivityOneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Activity", "ActivityTwo")
                        .WithMany("ParallelActivitiesTwo")
                        .HasForeignKey("ActivityTwoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ActivityOne");

                    b.Navigation("ActivityTwo");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.PauseReason", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Pool", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ReportType", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequest", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId");

                    b.HasOne("Lighthouse.Model.Entity.Asset", "Cluster")
                        .WithMany()
                        .HasForeignKey("ClusterID");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "InboundVoyage")
                        .WithMany()
                        .HasForeignKey("InboundVoyageId");

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany("SailingRequests")
                        .HasForeignKey("LocationId");

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "OutboundVoyage")
                        .WithMany()
                        .HasForeignKey("OutboundVoyageId");

                    b.HasOne("Lighthouse.Model.Entity.SailingRequest", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Vessel", "Vessel")
                        .WithMany()
                        .HasForeignKey("VesselId");

                    b.Navigation("Client");

                    b.Navigation("Cluster");

                    b.Navigation("CreatedBy");

                    b.Navigation("InboundVoyage");

                    b.Navigation("Location");

                    b.Navigation("OutboundVoyage");

                    b.Navigation("Parent");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vessel");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequestActivity", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.ActivityCategoryType", "ActivityCategoryType")
                        .WithMany()
                        .HasForeignKey("ActivityCategoryTypeId");

                    b.HasOne("Lighthouse.Model.Entity.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId");

                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.SailingRequestActivity", "DependantActivity")
                        .WithOne()
                        .HasForeignKey("Lighthouse.Model.Entity.SailingRequestActivity", "DependantActivityId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.SailingRequest", "SailingRequest")
                        .WithMany("SailingRequestActivities")
                        .HasForeignKey("SailingRequestId");

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("ActivityCategoryType");

                    b.Navigation("Area");

                    b.Navigation("Asset");

                    b.Navigation("CreatedBy");

                    b.Navigation("DependantActivity");

                    b.Navigation("SailingRequest");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequestAsset", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany("SailingRequestAssets")
                        .HasForeignKey("AssetId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.SailingRequest", "SailingRequest")
                        .WithMany("SailingRequestAssets")
                        .HasForeignKey("SailingRequestId");

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("Asset");

                    b.Navigation("CreatedBy");

                    b.Navigation("SailingRequest");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequestUserComment", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.SailingRequest", "SailingRequest")
                        .WithMany("SailingRequestUserComments")
                        .HasForeignKey("SailingRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("SailingRequest");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Setting", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "SettingsDefaultInitialPort")
                        .WithMany()
                        .HasForeignKey("SettingsDefaultInitialPortId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("SettingsDefaultInitialPort");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Site", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany("Sites")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Squad", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SquadEmployee", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Employee", "Employee")
                        .WithMany("SquadEmployees")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Squad", "Squad")
                        .WithMany("SquadEmployees")
                        .HasForeignKey("SquadId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Employee");

                    b.Navigation("Squad");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TankStatus", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BulkTransaction", "BulkTransaction")
                        .WithMany()
                        .HasForeignKey("BulkTransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("TankStatuses")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BulkTransaction");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TankType", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ToolBoxTalk", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Squad", "Squad")
                        .WithMany()
                        .HasForeignKey("SquadId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("Squad");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ToolBoxTalkEmployee", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Employee", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.ToolBoxTalk", "ToolBoxTalk")
                        .WithMany("ToolBoxTalkEmployees")
                        .HasForeignKey("ToolBoxTalkId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Employee");

                    b.Navigation("ToolBoxTalk");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ToolBoxTalkSite", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Site", "Site")
                        .WithMany("ToolBoxTalkSites")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.ToolBoxTalk", "ToolBoxTalk")
                        .WithMany("ToolBoxTalkSites")
                        .HasForeignKey("ToolBoxTalkId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Site");

                    b.Navigation("ToolBoxTalk");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Trailer", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequest", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.SailingRequest", "SailingRequest")
                        .WithMany("TransportRequests")
                        .HasForeignKey("SailingRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "SubmittedBy")
                        .WithMany()
                        .HasForeignKey("SubmittedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("TransportRequests")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("SailingRequest");

                    b.Navigation("SubmittedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestAttachment", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequest", "TransportRequest")
                        .WithMany("TransportRequestAttachments")
                        .HasForeignKey("TransportRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("TransportRequest");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargo", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BulkType", "BulkType")
                        .WithMany()
                        .HasForeignKey("BulkTypeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "FromAsset")
                        .WithMany()
                        .HasForeignKey("FromAssetId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Location", "FromLocation")
                        .WithMany()
                        .HasForeignKey("FromLocationId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "StatusUpdatedBy")
                        .WithMany()
                        .HasForeignKey("StatusUpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "SubmittedBy")
                        .WithMany()
                        .HasForeignKey("SubmittedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Asset", "ToAsset")
                        .WithMany()
                        .HasForeignKey("ToAssetId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Location", "ToLocation")
                        .WithMany()
                        .HasForeignKey("ToLocationId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestBulkCargoDangerousGood", "TransportRequestBulkCargoDangerousGood")
                        .WithMany()
                        .HasForeignKey("TransportRequestBulkCargoDangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequest", "TransportRequest")
                        .WithMany("TransportRequestBulkCargos")
                        .HasForeignKey("TransportRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("BulkType");

                    b.Navigation("CreatedBy");

                    b.Navigation("FromAsset");

                    b.Navigation("FromLocation");

                    b.Navigation("StatusUpdatedBy");

                    b.Navigation("SubmittedBy");

                    b.Navigation("ToAsset");

                    b.Navigation("ToLocation");

                    b.Navigation("TransportRequest");

                    b.Navigation("TransportRequestBulkCargoDangerousGood");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargoAttachment", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestBulkCargo", "TransportRequestBulkCargo")
                        .WithMany("TransportRequestBulkCargoAttachments")
                        .HasForeignKey("TransportRequestBulkCargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("TransportRequestBulkCargo");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargoDangerousGood", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.DangerousGood", "DangerousGood")
                        .WithMany("TransportRequestBulkCargoDangerousGoods")
                        .HasForeignKey("DangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("DangerousGood");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargoSnapshot", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequest", "TransportRequest")
                        .WithMany("TransportRequestBulkCargoSnapshots")
                        .HasForeignKey("TransportRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("TransportRequest");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargo", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "ApprovedBy")
                        .WithMany()
                        .HasForeignKey("ApprovedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Cargo", "Cargo")
                        .WithMany()
                        .HasForeignKey("CargoId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "FromAsset")
                        .WithMany()
                        .HasForeignKey("FromAssetId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Location", "FromLocation")
                        .WithMany()
                        .HasForeignKey("FromLocationId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "MovedBy")
                        .WithMany()
                        .HasForeignKey("MovedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "SpecialCargoProcessedBy")
                        .WithMany()
                        .HasForeignKey("SpecialCargoProcessedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "StatusUpdatedBy")
                        .WithMany()
                        .HasForeignKey("StatusUpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "SubmittedBy")
                        .WithMany()
                        .HasForeignKey("SubmittedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Asset", "ToAsset")
                        .WithMany()
                        .HasForeignKey("ToAssetId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Location", "ToLocation")
                        .WithMany()
                        .HasForeignKey("ToLocationId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "TransferProcessedBy")
                        .WithMany()
                        .HasForeignKey("TransferProcessedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestCargoBundling", "TransportRequestCargoBundling")
                        .WithMany()
                        .HasForeignKey("TransportRequestCargoBundlingId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequest", "TransportRequest")
                        .WithMany("TransportRequestCargos")
                        .HasForeignKey("TransportRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequest", "TransportRequestMovedFrom")
                        .WithMany()
                        .HasForeignKey("TransportRequestMovedFromId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.VendorWarehouse", "VendorWarehouse")
                        .WithMany()
                        .HasForeignKey("VendorWarehouseId");

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "ViaVendor")
                        .WithMany()
                        .HasForeignKey("ViaVendorId");

                    b.HasOne("Lighthouse.Model.Entity.VendorWarehouse", "ViaVendorWarehouse")
                        .WithMany()
                        .HasForeignKey("ViaVendorWarehouseId");

                    b.HasOne("Lighthouse.Model.Entity.User", "WasteProcessedBy")
                        .WithMany()
                        .HasForeignKey("WasteProcessedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("ApprovedBy");

                    b.Navigation("Cargo");

                    b.Navigation("CreatedBy");

                    b.Navigation("FromAsset");

                    b.Navigation("FromLocation");

                    b.Navigation("Location");

                    b.Navigation("MovedBy");

                    b.Navigation("SpecialCargoProcessedBy");

                    b.Navigation("StatusUpdatedBy");

                    b.Navigation("SubmittedBy");

                    b.Navigation("ToAsset");

                    b.Navigation("ToLocation");

                    b.Navigation("TransferProcessedBy");

                    b.Navigation("TransportRequest");

                    b.Navigation("TransportRequestCargoBundling");

                    b.Navigation("TransportRequestMovedFrom");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");

                    b.Navigation("VendorWarehouse");

                    b.Navigation("ViaVendor");

                    b.Navigation("ViaVendorWarehouse");

                    b.Navigation("WasteProcessedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargoAttachment", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestCargo", "TransportRequestCargo")
                        .WithMany("TransportRequestCargoAttachments")
                        .HasForeignKey("TransportRequestCargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("TransportRequestCargo");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargoBundling", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargoDangerousGood", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.DangerousGood", "DangerousGood")
                        .WithMany("TransportRequestCargoDangerousGoods")
                        .HasForeignKey("DangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestCargo", "TransportRequestCargo")
                        .WithMany("TransportRequestCargoDangerousGoods")
                        .HasForeignKey("TransportRequestCargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("DangerousGood");

                    b.Navigation("TransportRequestCargo");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargoSnapshot", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequest", "TransportRequest")
                        .WithMany("TransportRequestCargoSnapshots")
                        .HasForeignKey("TransportRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("TransportRequest");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestMaterialDetail", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "OffshoreInstallationAsset")
                        .WithMany()
                        .HasForeignKey("OffshoreInstallationId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.PackingUnit", "PackingUnit")
                        .WithMany()
                        .HasForeignKey("PackingUnitId");

                    b.HasOne("Lighthouse.Model.Entity.User", "SubmittedBy")
                        .WithMany()
                        .HasForeignKey("SubmittedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestBulkCargo", "TransportRequestBulkCargo")
                        .WithMany()
                        .HasForeignKey("TransportRequestBulkCargoId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestCargo", "TransportRequestCargo")
                        .WithMany()
                        .HasForeignKey("TransportRequestCargoId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequest", "TransportRequest")
                        .WithMany("TransportRequestMaterialDetails")
                        .HasForeignKey("TransportRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestMaterialDetailDangerousGood", "TransportRequestMaterialDetailDangerousGood")
                        .WithMany()
                        .HasForeignKey("TransportRequestMaterialDetailDangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("OffshoreInstallationAsset");

                    b.Navigation("PackingUnit");

                    b.Navigation("SubmittedBy");

                    b.Navigation("TransportRequest");

                    b.Navigation("TransportRequestBulkCargo");

                    b.Navigation("TransportRequestCargo");

                    b.Navigation("TransportRequestMaterialDetailDangerousGood");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestMaterialDetailDangerousGood", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestBulkCargoDangerousGood", "TransportRequestBulkCargoDangerousGood")
                        .WithMany()
                        .HasForeignKey("TransportRequestBulkCargoDangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestCargoDangerousGood", "TransportRequestCargoDangerousGood")
                        .WithMany()
                        .HasForeignKey("TransportRequestCargoDangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("TransportRequestBulkCargoDangerousGood");

                    b.Navigation("TransportRequestCargoDangerousGood");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestMaterialDetailSnapshot", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequest", "TransportRequest")
                        .WithMany("TransportRequestMaterialDetailSnapshots")
                        .HasForeignKey("TransportRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("TransportRequest");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestmaterialDetailAttachment", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestMaterialDetail", "TransportRequestmaterialDetail")
                        .WithMany("TransportRequestMaterialDetailAttachments")
                        .HasForeignKey("TransportRequestmaterialDetailId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("TransportRequestmaterialDetail");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Unit", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.User", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany("Users")
                        .HasForeignKey("ClientId");

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId");

                    b.OwnsMany("Lighthouse.Model.Entity.Role", "Roles", b1 =>
                        {
                            b1.Property<Guid>("UserId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            b1.Property<string>("Application")
                                .HasColumnType("nvarchar(max)");

                            b1.PrimitiveCollection<string>("Roles")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("UserId", "__synthesizedOrdinal");

                            b1.ToTable("Users");

                            b1.ToJson("Roles");

                            b1.WithOwner()
                                .HasForeignKey("UserId");
                        });

                    b.Navigation("Client");

                    b.Navigation("Location");

                    b.Navigation("Roles");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Vehicle", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Vendor", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("Vendors")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VendorWarehouse", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.District", "District")
                        .WithMany()
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany("VendorWarehouses")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("District");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Vessel", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VesselActivity", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Activity", "Activity")
                        .WithMany()
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "BilledAsset")
                        .WithMany()
                        .HasForeignKey("BilledAssetId");

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VesselActivities")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("Asset");

                    b.Navigation("BilledAsset");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VesselTank", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BulkType", "BulkType")
                        .WithMany()
                        .HasForeignKey("BulkTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TankType", "TankType")
                        .WithMany()
                        .HasForeignKey("TankTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Vessel", "Vessel")
                        .WithMany("VesselTanks")
                        .HasForeignKey("VesselId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BulkType");

                    b.Navigation("CreatedBy");

                    b.Navigation("TankType");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vessel");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Voyage", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId");

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CompletedByUser")
                        .WithMany()
                        .HasForeignKey("CompletedByUserId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Asset", "FinalAsset")
                        .WithMany()
                        .HasForeignKey("FinalAssetId");

                    b.HasOne("Lighthouse.Model.Entity.Asset", "InitialAsset")
                        .WithMany()
                        .HasForeignKey("InitialAssetId");

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId");

                    b.HasOne("Lighthouse.Model.Entity.User", "ReleasedBy")
                        .WithMany()
                        .HasForeignKey("ReleasedById");

                    b.HasOne("Lighthouse.Model.Entity.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId");

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Vessel", "Vessel")
                        .WithMany()
                        .HasForeignKey("VesselId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Area");

                    b.Navigation("Client");

                    b.Navigation("CompletedByUser");

                    b.Navigation("CreatedBy");

                    b.Navigation("FinalAsset");

                    b.Navigation("InitialAsset");

                    b.Navigation("Location");

                    b.Navigation("ReleasedBy");

                    b.Navigation("Site");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vessel");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageAttachment", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageAttachments")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageBillingPeriod", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BillingPeriod", "BillingPeriod")
                        .WithMany("VoyageBillingPeriods")
                        .HasForeignKey("BillingPeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany()
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BillingPeriod");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageBulk", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BulkType", "BulkType")
                        .WithMany()
                        .HasForeignKey("BulkTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageBulks")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BulkType");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageBulkQuantities", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.BulkType", "BulkType")
                        .WithMany()
                        .HasForeignKey("BulkTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany()
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BulkType");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargo", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany("VoyageCargoes")
                        .HasForeignKey("AssetId");

                    b.HasOne("Lighthouse.Model.Entity.Cargo", "Cargo")
                        .WithMany("VoyageCargoes")
                        .HasForeignKey("CargoId");

                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId");

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.District", "District")
                        .WithMany()
                        .HasForeignKey("DistrictId");

                    b.HasOne("Lighthouse.Model.Entity.Area", "LiftedAtArea")
                        .WithMany()
                        .HasForeignKey("LiftedAtAreaId");

                    b.HasOne("Lighthouse.Model.Entity.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId");

                    b.HasOne("Lighthouse.Model.Entity.Trailer", "Trailer")
                        .WithMany()
                        .HasForeignKey("TrailerId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestCargo", "TransportRequestCargo")
                        .WithMany()
                        .HasForeignKey("TransportRequestCargoId");

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId");

                    b.HasOne("Lighthouse.Model.Entity.VendorWarehouse", "VendorWarehouse")
                        .WithMany()
                        .HasForeignKey("VendorWarehouseId");

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "ViaVendor")
                        .WithMany()
                        .HasForeignKey("ViaVendorId");

                    b.HasOne("Lighthouse.Model.Entity.VendorWarehouse", "ViaVendorWarehouse")
                        .WithMany()
                        .HasForeignKey("ViaVendorWarehouseId");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargoLoad", "VoyageCargoLoad")
                        .WithMany("VoyageCargos")
                        .HasForeignKey("VoyageCargoLoadId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargo", "VoyageCargoParent")
                        .WithMany()
                        .HasForeignKey("VoyageCargoParentId");

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageCargoes")
                        .HasForeignKey("VoyageId");

                    b.HasOne("Lighthouse.Model.Entity.WeightCategory", "WeightCategory")
                        .WithMany()
                        .HasForeignKey("WeightCategoryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Asset");

                    b.Navigation("Cargo");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("District");

                    b.Navigation("LiftedAtArea");

                    b.Navigation("Site");

                    b.Navigation("Trailer");

                    b.Navigation("TransportRequestCargo");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");

                    b.Navigation("VendorWarehouse");

                    b.Navigation("ViaVendor");

                    b.Navigation("ViaVendorWarehouse");

                    b.Navigation("Voyage");

                    b.Navigation("VoyageCargoLoad");

                    b.Navigation("VoyageCargoParent");

                    b.Navigation("WeightCategory");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoBulk", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Asset", "Asset")
                        .WithMany("VoyageCargoBulks")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.BulkType", "BulkType")
                        .WithMany()
                        .HasForeignKey("BulkTypeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId");

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestBulkCargo", "TransportRequestBulkCargo")
                        .WithMany()
                        .HasForeignKey("TransportRequestBulkCargoId");

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargoBulkDangerousGood", "VoyageCargoBulkDangerousGood")
                        .WithMany()
                        .HasForeignKey("VoyageCargoBulkDangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageCargoBulks")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.WeightCategory", "WeightCategory")
                        .WithMany()
                        .HasForeignKey("WeightCategoryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Asset");

                    b.Navigation("BulkType");

                    b.Navigation("CreatedBy");

                    b.Navigation("Site");

                    b.Navigation("TransportRequestBulkCargo");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");

                    b.Navigation("Voyage");

                    b.Navigation("VoyageCargoBulkDangerousGood");

                    b.Navigation("WeightCategory");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoBulkDangerousGood", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.DangerousGood", "DangerousGood")
                        .WithMany("VoyageCargoBulkDangerousGoods")
                        .HasForeignKey("DangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestBulkCargoDangerousGood", "TransportRequestBulkCargoDangerousGood")
                        .WithMany()
                        .HasForeignKey("TransportRequestBulkCargoDangerousGoodId");

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("DangerousGood");

                    b.Navigation("TransportRequestBulkCargoDangerousGood");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoBulkSnapshot", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageCargoBulkSnapshots")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoDangerousGood", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.DangerousGood", "DangerousGood")
                        .WithMany("VoyageCargoDangerousGoods")
                        .HasForeignKey("DangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestCargoDangerousGood", "TransportRequestCargoDangerousGood")
                        .WithMany()
                        .HasForeignKey("TransportRequestCargoDangerousGoodId");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargo", "VoyageCargo")
                        .WithMany("VoyageCargoDangerousGoods")
                        .HasForeignKey("VoyageCargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("DangerousGood");

                    b.Navigation("TransportRequestCargoDangerousGood");

                    b.Navigation("VoyageCargo");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoInspection", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargo", "VoyageCargo")
                        .WithOne("VoyageCargoInspection")
                        .HasForeignKey("Lighthouse.Model.Entity.VoyageCargoInspection", "VoyageCargoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.VoyageInspection", "VoyageInspection")
                        .WithMany("InspectionCargos")
                        .HasForeignKey("VoyageInspectionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("VoyageCargo");

                    b.Navigation("VoyageInspection");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoInspectionAttachment", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargoInspection", "VoyageCargoInspection")
                        .WithMany("Attachments")
                        .HasForeignKey("VoyageCargoInspectionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("VoyageCargoInspection");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoLift", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.LoadCell", "LoadCell")
                        .WithMany()
                        .HasForeignKey("LoadCellId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargo", "VoyageCargo")
                        .WithMany("VoyageCargoLifts")
                        .HasForeignKey("VoyageCargoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Area");

                    b.Navigation("CreatedBy");

                    b.Navigation("LoadCell");

                    b.Navigation("UpdatedBy");

                    b.Navigation("VoyageCargo");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoLoad", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Driver", "Driver")
                        .WithMany()
                        .HasForeignKey("DriverId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Trailer", "Trailer")
                        .WithMany()
                        .HasForeignKey("TrailerId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Driver");

                    b.Navigation("Location");

                    b.Navigation("Trailer");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoSailingRequestActivity", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.SailingRequestActivity", "SailingRequestActivity")
                        .WithMany("VoyageCargoSailingRequestActivities")
                        .HasForeignKey("SailingRequestActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargo", "VoyageCargo")
                        .WithMany("VoyageCargoSailingRequestActivities")
                        .HasForeignKey("VoyageCargoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("SailingRequestActivity");

                    b.Navigation("UpdatedBy");

                    b.Navigation("VoyageCargo");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoSnapshot", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageCargoSnapshots")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageComment", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageComments")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageEvent", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageEvents")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageInspection", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithOne()
                        .HasForeignKey("Lighthouse.Model.Entity.VoyageInspection", "VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageLiftingJob", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.PauseReason", "PauseReason")
                        .WithMany("VoyageLiftingJobs")
                        .HasForeignKey("PauseReasonId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageLiftingJobs")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Area");

                    b.Navigation("CreatedBy");

                    b.Navigation("PauseReason");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageMaterialDetail", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.PackingUnit", "PackingUnit")
                        .WithMany()
                        .HasForeignKey("PackingUnitId");

                    b.HasOne("Lighthouse.Model.Entity.TransportRequestMaterialDetail", "transportRequestMaterialDetail")
                        .WithMany()
                        .HasForeignKey("TransportRequestMaterialDetailId");

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargoBulk", "VoyageCargoBulk")
                        .WithMany()
                        .HasForeignKey("VoyageCargoBulkId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargo", "VoyageCargo")
                        .WithMany("MaterialDetails")
                        .HasForeignKey("VoyageCargoId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("MaterialDetails")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.VoyageMaterialDetailDangerousGood", "VoyageMaterialDetailDangerousGood")
                        .WithMany()
                        .HasForeignKey("VoyageMaterialDetailDangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("PackingUnit");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Vendor");

                    b.Navigation("Voyage");

                    b.Navigation("VoyageCargo");

                    b.Navigation("VoyageCargoBulk");

                    b.Navigation("VoyageMaterialDetailDangerousGood");

                    b.Navigation("transportRequestMaterialDetail");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageMaterialDetailDangerousGood", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargoBulkDangerousGood", "VoyageCargoBulkDangerousGood")
                        .WithMany()
                        .HasForeignKey("VoyageBulkCargoDangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.VoyageCargoDangerousGood", "VoyageCargoDangerousGood")
                        .WithMany()
                        .HasForeignKey("VoyageCargoDangerousGoodId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("VoyageCargoBulkDangerousGood");

                    b.Navigation("VoyageCargoDangerousGood");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageMaterialDetailSnapshot", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageMaterialDetailSnapshots")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyagePlanningDetail", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyagePlanningDetails")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Area");

                    b.Navigation("CreatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageReservedArea", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageReservedAreas")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageSharer", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageSharers")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageSpecialNote", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Voyage", "Voyage")
                        .WithMany("VoyageSpecialNotes")
                        .HasForeignKey("VoyageId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Voyage");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.WeightCategory", b =>
                {
                    b.HasOne("Lighthouse.Model.Entity.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Lighthouse.Model.Entity.User", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("Location");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Activity", b =>
                {
                    b.Navigation("ParallelActivitiesOne");

                    b.Navigation("ParallelActivitiesTwo");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ActivityCategory", b =>
                {
                    b.Navigation("ActivityCategoryTypes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Area", b =>
                {
                    b.Navigation("AreaBlockingActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Asset", b =>
                {
                    b.Navigation("AssetLocations");

                    b.Navigation("ClientAssets");

                    b.Navigation("ClusterHistory");

                    b.Navigation("SailingRequestAssets");

                    b.Navigation("VoyageCargoBulks");

                    b.Navigation("VoyageCargoes");

                    b.Navigation("VoyageOffshoreLocations");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.BillingPeriod", b =>
                {
                    b.Navigation("VoyageBillingPeriods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Cargo", b =>
                {
                    b.Navigation("CargoCertificates");

                    b.Navigation("VoyageCargoes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.CargoFamily", b =>
                {
                    b.Navigation("CargoSizes");

                    b.Navigation("CargoTypes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Client", b =>
                {
                    b.Navigation("ClientAssets");

                    b.Navigation("ClientLocations");

                    b.Navigation("ClientNameHistory");

                    b.Navigation("ClientReportTypes");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.DangerousGood", b =>
                {
                    b.Navigation("DangerousGoodLocations");

                    b.Navigation("TransportRequestBulkCargoDangerousGoods");

                    b.Navigation("TransportRequestCargoDangerousGoods");

                    b.Navigation("VoyageCargoBulkDangerousGoods");

                    b.Navigation("VoyageCargoDangerousGoods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Employee", b =>
                {
                    b.Navigation("SquadEmployees");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireRequest", b =>
                {
                    b.Navigation("Cargoes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.HireStatement", b =>
                {
                    b.Navigation("HireStatementBulks");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.LiftingPlan", b =>
                {
                    b.Navigation("LiftingPlanEmployees");

                    b.Navigation("LiftingPlanResources");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Location", b =>
                {
                    b.Navigation("AssetLocations");

                    b.Navigation("Cargoes");

                    b.Navigation("ClientLocations");

                    b.Navigation("DangerousGoodLocations");

                    b.Navigation("Districts");

                    b.Navigation("SailingRequests");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.PauseReason", b =>
                {
                    b.Navigation("VoyageLiftingJobs");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequest", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("SailingRequestActivities");

                    b.Navigation("SailingRequestAssets");

                    b.Navigation("SailingRequestUserComments");

                    b.Navigation("TransportRequests");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequestActivity", b =>
                {
                    b.Navigation("VoyageCargoSailingRequestActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.SailingRequestUserComment", b =>
                {
                    b.Navigation("CommentReadByUsers");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Site", b =>
                {
                    b.Navigation("Areas");

                    b.Navigation("ToolBoxTalkSites");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Squad", b =>
                {
                    b.Navigation("SquadEmployees");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.ToolBoxTalk", b =>
                {
                    b.Navigation("ToolBoxTalkEmployees");

                    b.Navigation("ToolBoxTalkSites");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequest", b =>
                {
                    b.Navigation("TransportRequestAttachments");

                    b.Navigation("TransportRequestBulkCargoSnapshots");

                    b.Navigation("TransportRequestBulkCargos");

                    b.Navigation("TransportRequestCargoSnapshots");

                    b.Navigation("TransportRequestCargos");

                    b.Navigation("TransportRequestMaterialDetailSnapshots");

                    b.Navigation("TransportRequestMaterialDetails");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestBulkCargo", b =>
                {
                    b.Navigation("TransportRequestBulkCargoAttachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestCargo", b =>
                {
                    b.Navigation("TransportRequestCargoAttachments");

                    b.Navigation("TransportRequestCargoDangerousGoods");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.TransportRequestMaterialDetail", b =>
                {
                    b.Navigation("TransportRequestMaterialDetailAttachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Vendor", b =>
                {
                    b.Navigation("VendorWarehouses");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Vessel", b =>
                {
                    b.Navigation("HireStatements");

                    b.Navigation("VesselTanks");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.Voyage", b =>
                {
                    b.Navigation("BulkRequests");

                    b.Navigation("BulkTransactions");

                    b.Navigation("DeckUsages");

                    b.Navigation("LiftingPlans");

                    b.Navigation("MaterialDetails");

                    b.Navigation("OffshoreLocations");

                    b.Navigation("TankStatuses");

                    b.Navigation("TransportRequests");

                    b.Navigation("Vendors");

                    b.Navigation("VesselActivities");

                    b.Navigation("VoyageAttachments");

                    b.Navigation("VoyageBulks");

                    b.Navigation("VoyageCargoBulkSnapshots");

                    b.Navigation("VoyageCargoBulks");

                    b.Navigation("VoyageCargoSnapshots");

                    b.Navigation("VoyageCargoes");

                    b.Navigation("VoyageComments");

                    b.Navigation("VoyageEvents");

                    b.Navigation("VoyageLiftingJobs");

                    b.Navigation("VoyageMaterialDetailSnapshots");

                    b.Navigation("VoyagePlanningDetails");

                    b.Navigation("VoyageReservedAreas");

                    b.Navigation("VoyageSharers");

                    b.Navigation("VoyageSpecialNotes");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargo", b =>
                {
                    b.Navigation("MaterialDetails");

                    b.Navigation("VoyageCargoDangerousGoods");

                    b.Navigation("VoyageCargoInspection");

                    b.Navigation("VoyageCargoLifts");

                    b.Navigation("VoyageCargoSailingRequestActivities");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoInspection", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageCargoLoad", b =>
                {
                    b.Navigation("VoyageCargos");
                });

            modelBuilder.Entity("Lighthouse.Model.Entity.VoyageInspection", b =>
                {
                    b.Navigation("InspectionCargos");
                });
#pragma warning restore 612, 618
        }
    }
}
