﻿namespace Lighthouse.Service.Data.MasterData.Interface
{
    public interface ICargoService
    {
        Task<CargoModel> CreateAsync(CargoUpsertModel upsertModel, ClaimsPrincipal user);
        Task DeleteAsync(Guid id);
        Task<List<CargoModel>> GetAllAsync(MaintenanceCargoFilterModel filterModel);
        Task<List<CargoModel>> GetAllApprovedAsync();
        Task<List<CargoModel>> GetAllCargoesByTRAsync(Guid transportRequestId);
        Task<List<CargoModel>> GetAllByCCUAsync(Guid locationId, string ccuIdPart);
        Task<List<CargoModel>> GetAllByLocationAsync(Guid locationId);
        Task<CargoModel> GetByCcuIdAsync(string ccuId);
        Task<CargoModel> GetByIdAsync(Guid id);
        Task<CargoModel> UpdateAsync(Guid id, CargoUpsertModel upsertModel, ClaimsPrincipal user);
        Task ApproveCargo(Guid id, ClaimsPrincipal user);
        Task SetCertificateTestDate(CertificateTestDateModel model);
        Task<CargoModel> EnableDisableCargo(Guid cargoId, ClaimsPrincipal user);
        Task<List<CargoUnitTypeModel>> GetCargoDescriptions();
        Task<bool> ValidateForExtraAsync(Guid voyageId, string ccuId);
        Task<List<CargoModel>> GetAllCCUsAsync(ClaimsPrincipal user, CcuFilter filter, bool isContain = false);
        Task<CargoModel> SetCcuStatusAsync(Guid id, int status, ClaimsPrincipal user);
        Task<List<CargoModel>> GetAllAsyncIncludingAdhocForContain(UserModel user);
    }
}
