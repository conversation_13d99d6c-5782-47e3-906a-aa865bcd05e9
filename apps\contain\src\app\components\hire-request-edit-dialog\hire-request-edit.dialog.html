<p-dialog [draggable]="false" [closable]="false" header="Edit Hire Request" [modal]="true" [(visible)]="dialogVisible"
          [style]="{ width: '840px' }">
    <ng-template pTemplate="content">
        <div class="hire-request-create-dialog">
            <div class="grid-container">
                <form class="mt-32" [formGroup]="hireRequestEditForm" (ngSubmit)="submit()">
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Requested Date</span>
                            <p-calendar
                              [inputId]="'requestedDate'"
                              [tabindex]="0"
                              [readonlyInput]="true"
                              [showIcon]="false"
                              [showOnFocus]="false"
                              dateFormat="dd/mm/yy"
                              [showTime]="false"
                              formControlName="requestedDate"
                              (keydown)="$event.stopPropagation()"
                              appendTo="body">
                            </p-calendar>
                          </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Requested By</span>
                            <input
                                   type="text"
                                   pInputText
                                   [readonly]="true"
                                   label="Requested By"
                                   formControlName="requestedBy" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Description</span>
                            <p-dropdown
                                        [options]="cargoDescriptions()"
                                        styleClass="new-version"
                                        [filter]="true"
                                        formControlName="cargoDescriptionId"
                                        optionLabel="description"
                                        optionValue="cargoDescriptionId"
                                        inputId="cargoDescriptionId"
                                        [showClear]="true"
                                        appendTo="body" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Unit Quantity</span>
                            <p-inputNumber inputId="integeronly" formControlName="unitQuantity" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Client</span>
                            <p-dropdown
                                        [options]="clients()"
                                        [filter]="true"
                                        formControlName="clientId"
                                        styleClass="new-version"
                                        optionLabel="name"
                                        optionValue="clientId"
                                        inputId="client"
                                        [showClear]="true"
                                        appendTo="body" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Asset</span>
                            <p-dropdown
                                        [options]="assets()"
                                        [filter]="true"
                                        formControlName="assetId"
                                        optionLabel="name"
                                        optionValue="assetId"
                                        styleClass="new-version"
                                        inputId="asset"
                                        [showClear]="true"
                                        appendTo="body" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap pt-20 gap-16">
                        <div class="flex-column p-10 flex-1 p-8">
                            <p-checkbox
                                        formControlName="cstRequired"
                                        inputId="cstRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="cstRequired" class="fs-14 color-dark-gray">CST Required</label>
                        </div>
                        <div class="flex-column p-10 flex-1 p-8">
                            <p-checkbox
                                        formControlName="doorsRequired"
                                        inputId="doorsRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="doorsRequired" class="fs-14 color-dark-gray">Doors Required</label>
                        </div>
                        <div class="flex-column p-10 flex-1 p-8">
                            <p-checkbox
                                        formControlName="removableSidesRequired"
                                        inputId="removableSidesRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="removableSidesRequired" class="fs-14 color-dark-gray">Removable-Sides
                                Required</label>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap pb-20 gap-16">
                        <div class="flex-column p-10 flex-1 p-8">
                            <p-checkbox
                                        formControlName="netRequired"
                                        inputId="netRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="netRequired" class="fs-14 color-dark-gray">Net Required</label>
                        </div>
                        <div class="flex-column p-10 flex-1 p-8">
                            <p-checkbox
                                        formControlName="tarpaulinRequired"
                                        inputId="tarpaulinRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="tarpaulinRequired" class="fs-14 color-dark-gray">Tarp Required</label>
                        </div>
                        <div class="flex-column p-10 flex-1 p-8">
                            <p-checkbox
                                        formControlName="shelvesRequired"
                                        inputId="shelvesRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="shelvesRequired" class="fs-14 color-dark-gray">Shelves Required</label>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Off Waiting List Date</span>
                            <p-calendar
                                        [inputId]="'offWaitingListDate'"
                                        [tabindex]="0"
                                        [readonlyInput]="true"
                                        [showIcon]="true"
                                        dateFormat="dd/mm/yy"
                                        formControlName="offWaitingListDate"
                                        (keydown)="$event.stopPropagation()"
                                        appendTo="body">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>CCU Supplier</span>
                            <p-dropdown
                                        [options]="vendors()"
                                        [filter]="true"
                                        formControlName="vendorId"
                                        styleClass="new-version"
                                        optionLabel="vendorName"
                                        optionValue="vendorId"
                                        inputId="vendor"
                                        [showClear]="true"
                                        appendTo="body" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Order Taken By</span>
                            <input
                                   type="text"
                                   pInputText
                                   formControlName="orderTakenBy" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Verified Date Time</span>
                            <p-calendar
                                        [inputId]="'verifiedDateTime'"
                                        [showIcon]="true"
                                        [showTime]="true"
                                        [tabindex]="0"
                                        [readonlyInput]="true"
                                        [showOnFocus]="false"
                                        formControlName="verifiedDateTime"
                                        (keydown)="$event.stopPropagation()"
                                        appendTo="body">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Confirmed By</span>
                            <input
                                   type="text"
                                   pInputText
                                   formControlName="confirmedBy" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Collection Ref</span>
                            <input
                                   type="text"
                                   pInputText
                                   formControlName="collectionReference" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Planned Sailing Date</span>
                            <p-calendar
                                        [inputId]="'plannedSailingDate'"
                                        [tabindex]="0"
                                        [showTime]="true"
                                        [readonlyInput]="true"
                                        [showIcon]="true"
                                        dateFormat="dd/mm/yy"
                                        formControlName="plannedSailingDate"
                                        (keydown)="$event.stopPropagation()"
                                        appendTo="body">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <label for="comments-box">Comments</label>
                            <textarea
                                      id="comments-box"
                                      rows="5"
                                      cols="30"
                                      style="resize: none;"
                                      pInputTextarea
                                      formControlName="comments">
                            </textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <button
                class="btn-tertiary"
                type="button"
                (click)="hideDialog()">
            Cancel
        </button>
        <button
                class="btn-primary"
                type="button"
                (click)="submit()"
                [disabled]="!hireRequestEditForm.valid">
            Save
        </button>
    </ng-template>
</p-dialog>