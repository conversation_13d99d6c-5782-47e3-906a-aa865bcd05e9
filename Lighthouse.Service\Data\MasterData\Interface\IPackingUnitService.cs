﻿namespace Lighthouse.Service.Data.MasterData.Interface
{
    public interface IPackingUnitService
    {
        Task<IList<PackingUnitModel>> GetAsync(PackingUnitSearchModel model);

        Task<PackingUnitModel> GetPackingUnitByIdAsync(Guid id);

        Task<PackingUnitModel> PostAsync(PackingUnitUpsertModel model, Guid userId);

        Task<PackingUnitModel> PutAsync(Guid id, PackingUnitUpsertModel model, Guid userId);

        Task<bool> DeleteAsync(Guid id);
    }
}