namespace Lighthouse.Service.Data.MasterData {
    public class AssetService : IAssetService {

        private readonly IMapper mapper;
        private readonly IUnitOfWork unitOfWork;
        private readonly IClientAssetService _clientAssetService;
        private readonly IExportService exportService;
        private readonly IMobileWellService mobileWellService;
        private readonly IAssetLocationService _assetLocationService;
        private readonly IClusterService _clusterService;

        public AssetService(IMapper mapper,
            IUnitOfWork unitOfWork,
            IClientAssetService clientAssetService,
            IMobileWellService mobileWellService,
            IExportService exportService,
            IAssetLocationService assetLocationService,
            IClusterService clusterService) {
            this.mapper = mapper;
            this.unitOfWork = unitOfWork;
            this._clientAssetService = clientAssetService;
            this.exportService = exportService;
            this.mobileWellService = mobileWellService;
            this._assetLocationService = assetLocationService;
            this._clusterService = clusterService;
        }

        public async Task<IList<AssetModel>> GetAsync() {
            var assets = await unitOfWork.Repository<Asset>()
                .Query()
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .Include(x => x.Client)
                .Include(x => x.ClientAssets.OrderByDescending(x => x.StartDateTime))
                    .ThenInclude(x => x.Client)
                .Include(x => x.AssetLocations)
                    .ThenInclude(x => x.Location)
                .OrderBy(c => c.Name)
                .ToListAsync();

            var assetModels = assets.Select(u => mapper.Map<AssetModel>(u)).OrderBy(c => c.Name).ToList();
            foreach (var asset in assetModels) {
                SetClientAssetStartEndTime(asset);
                asset.IsClusterHead = await IsClusterHead(asset.AssetId, asset.AssetType);
                if (asset.IsClusterHead) {
                    var clusterChildren = await GetClusterChildren(asset.AssetId);
                    asset.ClusterChildren = clusterChildren.Select(x => x.Name).ToList();
                }
                if (asset.AssetLocations.Count > 0) {
                    asset.LocationIds = asset.AssetLocations.Select(x => x.LocationId).ToList();
                }
            }
            return assetModels;
        }

        public async Task<IList<AssetModel>> GetPortsAsync() {
            var ports = await unitOfWork.Repository<Asset>()
                .Query()
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .Include(x => x.AssetLocations)
                    .ThenInclude(x => x.Location)
                .OrderBy(c => c.Name)
                .Where(x => x.AssetType == AssetTypeConstant.Port)
                .ToListAsync();

            var assetModels = ports.Select(u => mapper.Map<AssetModel>(u)).OrderBy(c => c.Name).ToList();
            return assetModels;
        }

        public async Task<IList<AssetModel>> GetOffshoreInstallationsAsync() {
            var installations = await unitOfWork.Repository<Asset>()
                .Query()
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .Include(x => x.Client)
                .Include(x => x.ClientAssets.OrderByDescending(x => x.StartDateTime))
                    .ThenInclude(x => x.Client)
                .Include(x => x.AssetLocations)
                    .ThenInclude(x => x.Location)
                .Where(x => x.AssetType == AssetTypeConstant.Offshore || x.AssetType == AssetTypeConstant.Mobile)
                .OrderBy(c => c.Name)
                .ToListAsync();

            var assetModels = installations.Select(u => mapper.Map<AssetModel>(u)).ToList();
            return assetModels;
        }

        public async Task<IList<AssetModel>> GetWellsAsync() {
            var wells = await unitOfWork.Repository<Asset>()
                .Query()
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .Include(x => x.Client)
                .Include(x => x.ClientAssets.OrderByDescending(x => x.StartDateTime))
                    .ThenInclude(x => x.Client)
                .Include(x => x.AssetLocations)
                    .ThenInclude(x => x.Location)
                .Where(x => x.AssetType == AssetTypeConstant.Well)
                .OrderBy(c => c.Name)
                .ToListAsync();

            var assetModels = wells.Select(u => mapper.Map<AssetModel>(u)).ToList();
            return assetModels;
        }

        public async Task<IList<AssetModel>> GetClusterHeadAssetsAsync(Guid clientId) {
            var assets = await unitOfWork.Repository<Asset>()
                .Query(x => x.ClientId == clientId && !x.ClusterHeadId.HasValue && (x.AssetType == AssetTypeConstant.Offshore || x.AssetType == AssetTypeConstant.Well))
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .AsNoTracking()
                .OrderBy(c => c.Name)
                .ToListAsync();

            return assets.Select(u => mapper.Map<AssetModel>(u)).OrderBy(c => c.Name).ToList();
        }

        public async Task<IList<AssetModel>> GetOffshoreAssetsByLocationAsync(Guid locationId) {

            var assets = await unitOfWork.Repository<Asset>()
                .Query(x => x.AssetType == AssetTypeConstant.Offshore ||
                            x.AssetType == AssetTypeConstant.Well ||
                            x.AssetType == AssetTypeConstant.Mobile
                            )
                .Where(x => x.AssetLocations.Any(y => y.LocationId == locationId))
                .AsNoTracking()
                .OrderBy(c => c.Name)
                .ToListAsync();

            return assets.Select(asset => mapper.Map<AssetModel>(asset)).ToList();
        }

        public async Task<IList<AssetModel>> GetOwnedAssets() {
            var assets = await unitOfWork.Repository<Asset>()
                .Query(x => x.ClientId != null)
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .Include(x => x.Client)
                .Include(x => x.ClientAssets)
                .ThenInclude(x => x.Client)
                .AsNoTracking()
                .OrderBy(c => c.Name)
                .ToListAsync();

            var assetModels = assets.Select(u => mapper.Map<AssetModel>(u)).OrderBy(c => c.Name).ToList();
            foreach (var asset in assetModels) {
                SetClientAssetStartEndTime(asset);
                asset.IsClusterHead = await IsClusterHead(asset.AssetId, asset.AssetType);
            }
            return assetModels;
        }

        public async Task<IList<AssetModel>> GetWellAssets() {
            var wellAssets = await unitOfWork.Repository<Asset>()
                .Query(x => x.AssetType == AssetTypeConstant.Well)
                .OrderBy(c => c.Name)
                .ToListAsync();

            return wellAssets.Select(x => mapper.Map<AssetModel>(x)).OrderBy(x => x.Name).ToList();
        }

        public async Task<IList<AssetModel>> GetAssetsByLocation(Guid locationId) {
            return mapper.Map<List<AssetModel>>(await unitOfWork.Repository<Asset>()
                .Query()
                .Where(x => x.AssetLocations.Any(y => y.LocationId == locationId))
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .Include(x => x.Client)
                .Include(x => x.Client)
                .Include(x => x.ClientAssets)
                .Include(x => x.AssetLocations)
                    .ThenInclude(x => x.Location)
                .AsNoTracking()
                .OrderBy(c => c.Name)
                .ToListAsync());
        }

        public async Task<AssetModel> GetAssetsByIdAsync(Guid id) {
            var asset = await unitOfWork.Repository<Asset>()
                .Query()
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .Include(x => x.Client)
                .Include(x => x.ClientAssets)
                .Include(x => x.AssetLocations)
                    .ThenInclude(x => x.Location)
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.AssetId == id);

            var assetModel = mapper.Map<AssetModel>(asset);

            assetModel.LocationIds = asset.AssetLocations.Select(x => x.LocationId).ToList();

            return assetModel;
        }

        public async Task<AssetModel> PostAsync(AssetUpsertModel model, Guid userId, string timezone) {
            await unitOfWork.BeginTransactionAsync();
            if (model.AssetType != AssetTypeConstant.Well) {
                var existingAssetName = await unitOfWork.Repository<Asset>().Query(x => x.Name == model.Name).AnyAsync();

                if (existingAssetName) {
                    throw new Exception($"Asset with name {model.Name} already exists");
                }
            }

            // cant do this in mapping as the Asset entity does not require start and end time
            StartEndDateValidation(model);

            await AssetTypeValidation(model);

            var asset = new Asset();
            asset = mapper.Map(model, asset);
            asset = await unitOfWork.Repository<Asset>().CreateAsync(asset);

            if (asset.AssetType != AssetTypeConstant.Port && asset.AssetType != AssetTypeConstant.Mobile) {
                await CheckExistingAsset(asset, model, null);
            }

            asset.CreatedById = userId;

            if (asset.ClientId != null) {
                var clientAssetModel = new ClientAssetUpsertModel {
                    ClientId = model.ClientId.Value,
                    AssetId = asset.AssetId,
                    StartDateTime = model.StartDateTime.Value.AddTicks(-model.StartDateTime.Value.Ticks % TimeSpan.TicksPerSecond),
                    EndDateTime = model.EndDateTime.HasValue ? model.EndDateTime.Value.AddTicks(-model.EndDateTime.Value.Ticks % TimeSpan.TicksPerSecond) : null,
                };
                await _clientAssetService.PostAsync(clientAssetModel, userId, timezone);
            }

            if (asset.ClusterHeadId != null) {
                await _clusterService.CreateClusterHistoryAsync(model, asset.AssetId, userId);
            }

            if (model.LocationIds.Count > 0) {
                foreach (var locationId in model.LocationIds) {
                    await _assetLocationService.PostAsync(new AssetLocationUpsertModel {
                        AssetId = asset.AssetId,
                        LocationId = locationId
                    }, userId);
                }
            }

            await unitOfWork.SaveChangesAsync();

            await unitOfWork.CommitAsync();

            var assetModel = mapper.Map<AssetModel>(asset);
            return assetModel;
        }

        public async Task<AssetModel> PutAsync(Guid id, AssetUpsertModel model, Guid userId, string timezone, bool addNewAssetHistory) {

            var asset = await unitOfWork.Repository<Asset>()
                .Query(x => x.AssetId == id)
                .AsSplitQuery()
                .Include(x => x.CreatedBy)
                .Include(x => x.UpdatedBy)
                .Include(x => x.ClientAssets)
                .ThenInclude(x => x.Client)
                .FirstOrDefaultAsync();

            var originalAsset = mapper.Map<AssetModel>(asset);

            if (model.Name != asset.Name && model.AssetType != AssetTypeConstant.Well) {
                var existingAssetName = await unitOfWork.Repository<Asset>().Query(x => x.Name == model.Name).AnyAsync();

                if (existingAssetName) {
                    throw new Exception($"Asset with name {model.Name} already exists");
                }
            }

            if (asset.AssetType != model.AssetType) {
                throw new Exception($"Cannot change asset type once it has been created.");
            }

            await AssetTypeValidation(model);

            if (asset.AssetType != AssetTypeConstant.Port && asset.AssetType != AssetTypeConstant.Mobile) {
                await unitOfWork.BeginTransactionAsync();
                await UpdateAssetHistory(model, userId, asset, timezone, addNewAssetHistory);
                await unitOfWork.SaveChangesAsync();
                await unitOfWork.CommitAsync();
            } else {
                asset = mapper.Map(model, asset);
                asset.UpdatedById = userId;
                asset.UpdatedDate = DateTime.UtcNow;
                unitOfWork.Repository<Asset>().Update(asset);
                await unitOfWork.SaveChangesAsync();
            }

            var existingLocationIds = unitOfWork.Repository<AssetLocation>().Query().Where(crt => crt.AssetId == id).Select(x => x.LocationId).ToList();

            var locationsToBeAdded = model.LocationIds.Except(existingLocationIds);
            var locationsToBeDeleted = existingLocationIds.Except(model.LocationIds);

            foreach (var deleteLocation in locationsToBeDeleted) {
                await _assetLocationService.DeleteAsync(deleteLocation, asset.AssetId, userId);
            }

            foreach (var addLocation in locationsToBeAdded) {
                await _assetLocationService.PostAsync(new AssetLocationUpsertModel {
                    AssetId = asset.AssetId,
                    LocationId = addLocation,
                }, userId);
            }

            var assetModel = mapper.Map<AssetModel>(asset);

            await CreateAuditLog(assetModel, originalAsset, userId, "UPDATE");

            return assetModel;
        }

        private async Task UpdateAssetHistory(AssetUpsertModel model, Guid userId, Asset asset, string timezone, bool addNewAssetHistory) {
            // cant do this in mapping as the Asset entity does not require start and end time
            StartEndDateValidation(model);

            var isClusterHead = await IsClusterHead(asset.AssetId, asset.AssetType);
            var operatorChanged = asset.ClientId != model.ClientId && model.ClientId != null;

            var currentClientAsset = asset.ClientAssets.OrderByDescending(x => x.StartDateTime).FirstOrDefault();
            var currentClusterHead = unitOfWork.Repository<ClusterHistory>()
                .Query(x => x.ClusterChildId == asset.AssetId)
                .OrderByDescending(x => x.StartDateTime)
                .FirstOrDefault();
            var assetModel = mapper.Map<AssetModel>(asset);

            if (addNewAssetHistory) {
                if (currentClientAsset.EndDateTime == null) {
                    if (model.StartDateTime <= currentClientAsset.StartDateTime) {
                        var currentClientAssetModel = mapper.Map<ClientAssetModel>(currentClientAsset);
                        throw new Exception($"Tried to set end date for {currentClientAssetModel.ClientName} owning {model.Name} to be before start date.");
                    }
                }
                if (assetModel.ClientAssets.Any(x => model.StartDateTime < x.EndDateTime)) {
                    throw new Exception("Cannot add history that precedes first entry.");
                }
                await CheckExistingOwnershipDates(model, assetModel.ClientAssets.ToList());
                var clientAssetModel = new ClientAssetUpsertModel {
                    ClientId = model.ClientId.Value,
                    AssetId = asset.AssetId,
                    StartDateTime = model.StartDateTime.Value,
                    EndDateTime = model.EndDateTime,
                    IsClusterHead = isClusterHead,
                };
                await _clientAssetService.PostAsync(clientAssetModel, userId, timezone);

                if (model.ClusterHeadId != null) {
                    if (currentClusterHead != null && currentClusterHead.EndDateTime == null) {
                        currentClusterHead.EndDateTime = model.StartDateTime;
                    }
                    await _clusterService.CreateClusterHistoryAsync(model, asset.AssetId, userId);
                }
            } else {
                var removeClientAsset = assetModel.ClientAssets.Where(x => x.ClientAssetId == currentClientAsset.ClientAssetId).FirstOrDefault();
                assetModel.ClientAssets.Remove(removeClientAsset);
                await CheckExistingOwnershipDates(model, assetModel.ClientAssets.ToList());
                var clientAssetModel = new ClientAssetUpsertModel {
                    ClientId = model.ClientId.Value,
                    AssetId = asset.AssetId,
                    StartDateTime = model.StartDateTime.Value,
                    EndDateTime = model.EndDateTime,
                };
                await _clientAssetService.PutAsync(currentClientAsset.ClientAssetId, clientAssetModel, userId, timezone);

                if (currentClusterHead != null && currentClusterHead.EndDateTime == null && currentClusterHead.ClusterHeadId != model.ClusterHeadId) {
                    currentClusterHead.EndDateTime = model.StartDateTime;
                    await _clusterService.CreateClusterHistoryAsync(model, asset.AssetId, userId);
                } else if (currentClusterHead == null && asset.ClusterHeadId != model.ClusterHeadId) {
                    await _clusterService.CreateClusterHistoryAsync(model, asset.AssetId, userId);
                } else if (currentClusterHead != null && model.ClusterHeadId != null) {
                    currentClusterHead.StartDateTime = model.StartDateTime.Value;
                    currentClusterHead.EndDateTime = model.EndDateTime.HasValue ? model.EndDateTime.Value : null;
                }
            }

            asset = mapper.Map(model, asset);
            asset.UpdatedById = userId;
            asset.UpdatedDate = DateTime.UtcNow;
            unitOfWork.Repository<Asset>().Update(asset);

            if (isClusterHead && operatorChanged) {
                await UpdateClusterChildren(asset.AssetId, model, userId, timezone);
            }
        }

        private async Task CheckExistingOwnershipDates(AssetUpsertModel model, List<ClientAssetModel> clientAssets) {
            var existingHistoryAtDates = false;

            var ownershipErrorMessage = model.EndDateTime != null ?
                $"{model.Name} is owned by another operator between start date and end date" :
                $"{model.Name} is owned by another operator at start date";


            if (model.EndDateTime == null) {
                existingHistoryAtDates = clientAssets.Any(x => x.StartDateTime >= model.StartDateTime ||
                                                               (x.EndDateTime.HasValue && model.StartDateTime < x.EndDateTime));
            } else {
                existingHistoryAtDates = clientAssets.Any(y =>
                (model.StartDateTime >= y.StartDateTime && ((y.EndDateTime.HasValue && model.StartDateTime < y.EndDateTime) || !y.EndDateTime.HasValue))
                ||
                (model.EndDateTime > y.StartDateTime && ((y.EndDateTime.HasValue && model.EndDateTime <= y.EndDateTime) || !y.EndDateTime.HasValue))
                ||
                (model.StartDateTime <= y.StartDateTime && (y.EndDateTime.HasValue && model.EndDateTime >= y.EndDateTime)));
            }

            if (existingHistoryAtDates) {
                throw new Exception(ownershipErrorMessage);
            }
        }

        public async Task<bool> DeleteAsync(Guid id, Guid userId) {
            var asset = await unitOfWork.Repository<Asset>()
                .Query(x => x.AssetId == id)
                .FirstOrDefaultAsync();

            var bulkTransactions = await unitOfWork.Repository<BulkTransaction>().Query().AnyAsync(x => x.AssetId == id);
            var deckUsages = await unitOfWork.Repository<DeckUsage>().Query().AnyAsync(x => x.AssetId == id);
            var vesselActivities = await unitOfWork.Repository<VesselActivity>().Query().AnyAsync(x => x.AssetId == id);
            var voyageCargoBulks = await unitOfWork.Repository<VoyageCargoBulk>().Query().AnyAsync(x => x.AssetId == id);
            var materialDetails = await unitOfWork.Repository<VoyageMaterialDetail>().ExistsAsync(x => x.VoyageCargoBulk.AssetId == id || x.VoyageCargo.AssetId == id);
            var distances = await unitOfWork.Repository<Distance>().ExistsAsync(x => x.ToAssetId == id || x.BaseAssetId == id);
            if (asset.AssetType == AssetTypeConstant.Well) {
                var mobileWells = await unitOfWork.Repository<MobileWell>().Query(x => x.WellId == id).ToListAsync();
                foreach (var mobileWell in mobileWells) {
                    await mobileWellService.IsMobileWellBeingUsed(mobileWell);
                }
            }
            if (bulkTransactions || deckUsages || vesselActivities || voyageCargoBulks || materialDetails) {
                throw new Exception("The Asset is currently being used, the operation can’t be completed.");
            }

            if (distances) {
                throw new Exception("The Asset is currently being used in a distance, the operation can’t be completed.");
            }

            if (asset != null) {
                try {
                    asset.Deleted = true;
                    unitOfWork.Repository<Asset>().Update(asset);
                    var assetModel = mapper.Map<AssetModel>(asset);
                    var isClusterHead = await IsClusterHead(asset.AssetId, asset.AssetType);
                    if (isClusterHead) {
                        var clusterchildren = await GetClusterChildren(asset.AssetId);
                        foreach (var child in clusterchildren) {
                            child.ClusterHeadId = null;
                            unitOfWork.Repository<Asset>().Update(child);
                        }
                    }
                    await unitOfWork.Repository<Distance>().DeleteBatchAsync(x => x.ToAssetId == id || x.BaseAssetId == id);
                    await unitOfWork.Repository<ClientAsset>().DeleteBatchAsync(x => x.AssetId == id);
                    await unitOfWork.Repository<MobileWell>().DeleteBatchAsync(x => x.MobileId == id || x.WellId == id);
                    await CreateAuditLog(assetModel, null, userId, "DELETE");
                    await unitOfWork.SaveChangesAsync();
                    return true;
                } catch (Exception ex) {
                    return false;
                }
            }
            return false;
        }

        public async Task AssetTypeValidation(AssetUpsertModel model) {
            if (string.IsNullOrWhiteSpace(model.AssetType)) {
                throw new Exception("Asset type must have a value");
            }

            if ((model.AssetType == AssetTypeConstant.Offshore || model.AssetType == AssetTypeConstant.Well) &&
                (model.ClientId == null || model.StartDateTime == null)) {
                throw new Exception($"Asset needs to be linked to an Operator and requires a start date if asset type is {AssetTypeConstant.GetDescription(model.AssetType)}");
            }

            if ((model.AssetType == AssetTypeConstant.Port || model.AssetType == AssetTypeConstant.Mobile) &&
                ((model.ClientId != Guid.Empty && model.ClientId != null) || model.StartDateTime.HasValue || model.EndDateTime.HasValue)) {
                throw new Exception($"Asset does not need to be linked to an Operator or require a start and end date if asset type is {AssetTypeConstant.GetDescription(model.AssetType)}");
            }

            if ((model.AssetType == AssetTypeConstant.Offshore || model.AssetType == AssetTypeConstant.Well) && model.ClientId != null) {
                var client = await unitOfWork.Repository<Client>().Query(x => x.ClientId == model.ClientId).FirstOrDefaultAsync();
                if (!client.IsActive) {
                    throw new Exception($"{client.Name} is not active.");
                }
            }

            if ((model.AssetType == AssetTypeConstant.Mobile || model.AssetType == AssetTypeConstant.Port) && model.ClusterHeadId != null) {
                throw new Exception($"{AssetTypeConstant.GetDescription(model.AssetType)} asset cannot be apart of a cluster");
            }
        }

        private async Task CheckExistingAsset(Asset asset, AssetUpsertModel model, ClientAsset currentClientAsset) {
            var assets = await unitOfWork.Repository<Asset>()
                .Query()
                .ToListAsync();

            if (model.ClusterHeadId != null) {
                await ClusterValidation(assets, asset, model);
            }
        }

        private void StartEndDateValidation(AssetUpsertModel model) {
            if (model.AssetType == AssetTypeConstant.Port || model.AssetType == AssetTypeConstant.Mobile) {
                return;
            }
            if (model.StartDateTime.HasValue && model.StartDateTime.Value.Second != 0) {
                model.StartDateTime = DateTimezoneFormatting.ResetToWholeMinute(model.StartDateTime.Value);
            }

            if (model.EndDateTime.HasValue && model.EndDateTime.Value.Second != 0) {
                model.EndDateTime = DateTimezoneFormatting.ResetToWholeMinute(model.EndDateTime.Value);
            }
        }

        private void SetClientAssetStartEndTime(AssetModel asset) {
            if (asset.AssetType == AssetTypeConstant.Port || asset.AssetType == AssetTypeConstant.Mobile) {
                return;
            }
            if (asset.ClientAssets.Count > 0) {
                var getCurrentClientLocation = asset.ClientAssets.OrderByDescending(x => x.StartDateTime).FirstOrDefault();
                asset.StartDateTime = getCurrentClientLocation.StartDateTime;
                asset.EndDateTime = getCurrentClientLocation.EndDateTime;
            }
        }

        private async Task ClusterValidation(List<Asset> assets, Asset asset, AssetUpsertModel model) {
            if (model.ClusterHeadId == asset.AssetId) {
                throw new Exception("Asset cannot be the cluster head for itself");
            }

            var sameLocationOperatorClusterHead = assets.Where(x => x.AssetId == model.ClusterHeadId && x.ClientId == model.ClientId).Any();

            if (!sameLocationOperatorClusterHead) {
                throw new Exception("Cluster head must be of a asset with the same operator");
            }

            var existingClusterChild = assets.Where(x => x.AssetId == model.ClusterHeadId && x.ClusterHeadId.HasValue).Any();

            if (existingClusterChild) {
                throw new Exception("Cluster head selected is already a child of a cluster");
            }

            if (await IsClusterHead(asset.AssetId, asset.AssetType) && model.ClusterHeadId != null) {
                throw new Exception("Asset is already the cluster head of a cluster.");
            }

            var mobileClusterHead = assets.Where(x => x.AssetId == model.ClusterHeadId && (x.AssetType == AssetTypeConstant.Mobile || x.AssetType == AssetTypeConstant.Port)).Any();

            if (mobileClusterHead) {
                throw new Exception($"{AssetTypeConstant.GetDescription(model.AssetType)} assets cannot be cluster heads");
            }
        }

        public async Task<bool> IsClusterHead(Guid assetId, string assetType) {
            if (assetType == AssetTypeConstant.Port || assetType == AssetTypeConstant.Mobile) {
                return false;
            }
            return await unitOfWork.Repository<Asset>().ExistsAsync(x => x.ClusterHeadId == assetId);
        }

        private async Task<IList<Asset>> GetClusterChildren(Guid assetId) {
            var clusterChildren = await unitOfWork.Repository<Asset>().Query(x => x.ClusterHeadId == assetId).ToListAsync();

            return clusterChildren;
        }

        private async Task UpdateClusterChildren(Guid assetId, AssetUpsertModel model, Guid userId, string timezone) {
            var clusterChildren = await unitOfWork.Repository<Asset>()
                .Query(x => x.ClusterHeadId == assetId)
                .AsSplitQuery()
                .Include(x => x.ClientAssets)
                    .ThenInclude(x => x.Client)
                .ToListAsync();

            var clusterChildrenModels = clusterChildren.Select(x => mapper.Map<AssetModel>(x)).ToList();
            var startDate = DateTimezoneFormatting.ResetToWholeMinute(model.StartDateTime.Value);
            DateTime? endDate = model.EndDateTime.HasValue ? DateTimezoneFormatting.ResetToWholeMinute(model.EndDateTime.Value) : null;

            foreach (var child in clusterChildrenModels) {
                if (model.UpdateChildsOperators) {
                    child.ClientId = model.ClientId;
                    child.StartDateTime = startDate;
                    child.EndDateTime = endDate;
                    await UpdateAssetHistory(child, userId, clusterChildren.Where(x => x.AssetId == child.AssetId).FirstOrDefault(), timezone, true);
                } else {
                    var updateClusterChild = clusterChildren.Where(x => x.AssetId == child.AssetId).FirstOrDefault();
                    updateClusterChild.ClusterHeadId = null;
                    updateClusterChild.UpdatedById = userId;
                    updateClusterChild.UpdatedDate = DateTime.UtcNow;
                    unitOfWork.Repository<Asset>().Update(updateClusterChild);
                }
            }
        }

        public async Task<Asset> GetParentOfAssetOrReturnSelf(Asset asset, DateTime startDate, DateTime endDate) {
            if (asset.AssetType == AssetTypeConstant.Mobile) {
                asset = await mobileWellService.GetWellWhereMobileAssetWasDuringDates(asset.AssetId, startDate, endDate, false, null, null, "");
            }

            if (asset.ClusterHeadId != null) {
                asset = await unitOfWork.Repository<Asset>().Query(x => x.AssetId == asset.ClusterHeadId.Value).FirstOrDefaultAsync();
            }
            await _clientAssetService.SaveChangesAsync();

            return asset;
        }

        public async Task<byte[]> ExportAssets() {
            var assets = await GetAsync();
            assets = assets.OrderBy(x => x.Name).ToList();
            return exportService.ConvertAssetsToExcel(assets);
        }

        public async Task CreateAuditLog(AssetModel asset, AssetModel originalAsset, Guid userId, string action) {
            if (action == "UPDATE") {
                var type = typeof(AssetModel);
                foreach (var propertyInfo in type.GetProperties()) {
                    if (propertyInfo.Name == "Name" || propertyInfo.Name == "ClientName" || propertyInfo.Name == "ClusterHeadId") {
                        var originalValue = propertyInfo.GetValue(originalAsset);
                        var newValue = propertyInfo.GetValue(asset);

                        var oldClusterHead = new Asset();
                        var newClusterHead = new Asset();
                        if (propertyInfo.Name == "ClusterHeadId") {
                            oldClusterHead = originalValue != null ? await unitOfWork.Repository<Asset>().Query(x => x.AssetId.ToString() == originalValue.ToString()).FirstOrDefaultAsync() : null;
                            newClusterHead = newValue != null ? await unitOfWork.Repository<Asset>().Query(x => x.AssetId.ToString() == newValue.ToString()).FirstOrDefaultAsync() : null;
                        }

                        if (!object.Equals(originalValue, newValue)) {
                            var auditLog = new AuditLog {
                                TypeOfAction = action,
                                TableName = "Assets",
                                UserId = userId,
                                PrimaryIndicator = $"Asset Name: {asset.Name}",
                                FieldName = propertyInfo.Name == "ClusterHeadId" ? "Cluster Head" : propertyInfo.Name,
                                OldValue = propertyInfo.Name == "ClusterHeadId" && oldClusterHead != null ? oldClusterHead.Name : originalValue?.ToString(),
                                NewValue = propertyInfo.Name == "ClusterHeadId" && newClusterHead != null ? newClusterHead.Name : newValue?.ToString(),
                            };
                            await unitOfWork.Repository<AuditLog>().CreateAsync(auditLog);
                        }
                    }
                }
            } else {
                var auditLog = new AuditLog {
                    TypeOfAction = action,
                    TableName = "Assets",
                    UserId = userId,
                    PrimaryIndicator = $"Asset Name: {asset.Name}",
                };

                await unitOfWork.Repository<AuditLog>().CreateAsync(auditLog);
            }
            await unitOfWork.SaveChangesAsync();
        }
    }
}
