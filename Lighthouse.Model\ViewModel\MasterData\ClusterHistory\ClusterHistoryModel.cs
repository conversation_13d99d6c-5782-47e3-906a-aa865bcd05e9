﻿namespace Lighthouse.Model.ViewModel.MasterData.ClusterHistory {
    public class ClusterHistoryModel {
        public Guid ClusterHistoryId { get; set; }
        public Guid ClusterHeadId { get; set; }
        public string ClusterHeadName { get; set; }
        public Guid ClusterChildId { get; set; }
        public string ClusterChildName { get; set; }
        public DateTime StartDateTime { get; set; }
        public DateTime? EndDateTime { get; set; }
        public Guid CreatedById { get; set; }
        public string CreatedByName { get; set; }
        public Guid? UpdatedById { get; set; }
        public string UpdatedByName { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string Status {
            get {
                if (StartDateTime <= DateTime.UtcNow && (EndDateTime == null || EndDateTime > DateTime.UtcNow)) {
                    return "Current";
                } else if(StartDateTime > DateTime.UtcNow) {
                    return "Future";
                } else {
                    return "Previous";
                }
            }
        }
    }
}
