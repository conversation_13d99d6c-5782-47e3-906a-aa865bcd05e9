import { Component, computed, DestroyRef, inject, Input, OnInit, signal } from '@angular/core';
import { NgIf } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { HeaderNavComponent } from 'libs/components/src/lib/components/header-nav/header-nav.component';
import { NavBar } from 'libs/components/src/lib/interfaces/nav-bar.interface';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { CheckboxModule } from 'primeng/checkbox';
import { Store } from '@ngrx/store';
import {
  transportRequestFeature
} from 'libs/services/src/lib/services/transport-requests/store/features/transport-request.feature';
import { DividerModule } from 'primeng/divider';
import { CardModule } from 'primeng/card';
import {
  TransportRequestCargoSnapshotActions
} from 'libs/services/src/lib/services/transport-requests/store/actions/transport-request-cargo-snapshot.actions';
import { transportRequestCargoSnapshotFeature } from 'libs/services/src/lib/services/transport-requests/store/features';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  TransportRequestCargoActions
} from 'libs/services/src/lib/services/transport-requests/store/actions/transport-request-cargo.actions';
import {
  TransportRequestCargoFilterList
} from 'libs/services/src/lib/services/transport-requests/interfaces/transport-request-cargo-filter-list.interface';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { VoyageDirection } from '../../../../../../libs/services/src/lib/services/voyages/enums/voyage-direction.enum';
import { lookaheadFeature } from 'libs/services/src/lib/services/lookahead/store/features';
import { CompareActions } from '../../store/actions/compare-actions';
import { filter } from 'rxjs';

@Component({
  selector: 'request-cargo-list',
  templateUrl: './cargo-list.component.html',
  styleUrls: ['./cargo-list.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    ButtonModule,
    TabViewModule,
    HeaderNavComponent,
    RouterOutlet,
    CheckboxModule,
    DividerModule,
    CardModule,
    ReactiveFormsModule,
    ProgressSpinnerModule,
    DropdownModule,
    MultiSelectModule
  ]
})
export class CargoListComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  @Input() sailingRequestId = '';
  @Input() isBulkReq: boolean = false

  version = new FormControl('');

  router = inject(Router);
  route = inject(ActivatedRoute);
  store = inject(Store);
  transportRequestId = this.store.selectSignal(
    transportRequestFeature.selectTransportRequestId
  );
  transportRequest = this.store.selectSignal(transportRequestFeature.selectTransportRequest);
  transportRequestCargoVersions = this.store.selectSignal(transportRequestCargoSnapshotFeature.selectTransportRequestCargoSnapshotVersions);

  sailingRequest = this.store.selectSignal(
    lookaheadFeature.selectSailingRequest
  );

  type = signal('');

  filterForm = new FormGroup({
    assets: new FormControl('All'),
    isCancelled: new FormControl(false),
    version: new FormControl(''),
    installationId: new FormControl<string[]>([])
  });

  installations = computed(() => {
    const cargos = this.transportRequest()?.transportRequestCargos ?? [];
    const bulks = this.transportRequest()?.transportRequestBulkCargos ?? [];

    let allAssets = []

    if (this.transportRequest()?.voyageDirection == VoyageDirection.Inbound) {
      allAssets = [
        ...cargos.map(c => ({ id: c.fromAssetId, name: c.fromAssetName })),
        ...bulks.map(b => ({ id: b.fromAssetId, name: b.fromAssetName })),
      ];
    } else {
      allAssets = [
        ...cargos.map(c => ({ id: c.toAssetId, name: c.toAssetName })),
        ...bulks.map(b => ({ id: b.toAssetId, name: b.toAssetName })),
      ];
    }

    allAssets = allAssets.filter(asset => asset.id && asset.name && asset.name.trim() !== '');

    const uniqueAssets = Array.from(
      new Map(allAssets.map(asset => [asset.id, asset])).values()
    );

    return uniqueAssets

  })

  showDropdown = computed(() => {
    if (this.installations().length > 1)
      return true

    return false;
  })

  navItems = computed<NavBar[]>(() => {
    const fragment = this.route.snapshot.fragment;
    return [
      {
        title: 'Cargo',
        link: `/transport-requests/${this.sailingRequestId}/${this.transportRequestId()}/cargo`,
        fragment: fragment,
        permissions: [],
      },
      {
        title: 'Bulks',
        link: `/transport-requests/${this.sailingRequestId
          }/${this.transportRequestId()}/bulks`,
        fragment: fragment,
        permissions: [],
        disable: !(this.isBulkReq)
      },
      {
        title: 'Material Details',
        link: `/transport-requests/${this.sailingRequestId
          }/${this.transportRequestId()}/material-details`,
        fragment: fragment,
        permissions: [],
      },
    ];
  });

  disabledCompareBtn = computed(() => {
    return this.transportRequestCargoVersions().length < 2;
  });

  ngOnInit(): void {
    this.store.dispatch(
         TransportRequestCargoSnapshotActions.load_Transport_Request_Cargo_Versions(
           { transportRequestId: this.transportRequestId() }
         )
       );
       this.handleNavigation();
       this.updateTransportRequestType();
       
       this.store
         .select(
           transportRequestCargoSnapshotFeature.selectTransportRequestCargoSnapshotVersions
         )
         .pipe(takeUntilDestroyed(this.destroyRef))
         .subscribe((versions) => {
           this.version.setValue(versions[0]?.transportRequestCargoSnapshotId);
         });

    this.filterForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.store.dispatch(
          TransportRequestCargoActions.filter_Transport_Request_Cargo_List({
            filter: value as TransportRequestCargoFilterList,
          })
        );
      });
  }

  exportExcel() {
    const installationIds = this.filterForm.get('installationId')?.value ?? [];
    this.store.dispatch(TransportRequestCargoActions.export_Excel({ transportRequestId: this.transportRequestId(), installationIds: installationIds }))
  }

  navigateToCreateEdit(): void {
    const fragment = this.route.snapshot.fragment;
    const urlWithoutFragment = this.router.url.split('#')[0];
    const urlParts = fragment ? urlWithoutFragment.split('/') : this.router.url.split('/');
    const type = urlParts[4];

    this.router.navigate([`transport-requests/${this.sailingRequestId}/${this.transportRequestId()}/cargo-list/create-edit/${type}`]);
  }

  changeTarget(snapshotId: string) {
    this.store.dispatch(
      CompareActions.set_Target_Snapshot_Id({ snapshotId })
    );
  }

  handleNavigateCompare() {
    this.router.navigate(
      [
        'compare',
        this.sailingRequest()?.sailingRequestId,
        this.transportRequestId(),
      ],
      {
        queryParams: {
          view: this.route.children[0]?.snapshot.routeConfig?.path,
        },
      }
    );
  }

  handleNavigation() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        this.updateTransportRequestType();
      });
  }

  updateTransportRequestType() {
    switch (this.router.url.split('/')[4]) {
      case 'cargo':
        this.type.set('Cargo');
        break;
      case 'bulks':
        this.type.set('Bulks');
        break;
      case 'material-details':
        this.type.set('Material Details');
        break;
    }
  }
}
