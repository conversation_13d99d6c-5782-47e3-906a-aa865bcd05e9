<div [formGroup]="filterForm">
  <p-card class="container w-100">
    <div class="p-16">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="header-cargo-list d-flex align-items-center gap-20">
          <div>
            <div class="d-flex justify-content-center label-secondary">
              VERSION
            </div>
            <div
              class="d-flex justify-content-center label"
              *ngIf="transportRequestCargoVersions().length"
            >
              {{ transportRequestCargoVersions()[0].versionNumber }}.0
            </div>
          </div>
        </div>

        <div class="d-flex gap-20">
          <div class="d-flex align-items-center">
            <p-checkbox
              formControlName="isCancelled"
              inputId="isCancelled"
              [binary]="true"
            ></p-checkbox>
            <label for="isCancelled" class="fs-14 color-dark-gray"
              >Hide Cancelled</label
            >
          </div>
          <ng-container *ngIf="!transportRequest()?.isComplete">
            <p-button
              label="Create or Edit"
              (onClick)="navigateToCreateEdit()"
              type="button"
              [outlined]="true"
              severity="secondary"
            >
              <img style="margin-right: 5px" src="assets/edit.svg" />
            </p-button>

            <p-dropdown
              [options]="transportRequestCargoVersions()"
              optionLabel="version"
              optionValue="transportRequestCargoSnapshotId"
              [formControl]="version"
              placeholder="Select Versions"
              styleClass="new-version"
              panelStyleClass="new-version-panel"
              [style.min-width.px]="240"
              (onChange)="changeTarget($event.value)"
            />

            <button
              class="btn-secondary"
              type="button"
              (click)="handleNavigateCompare()"
              [disabled]="disabledCompareBtn()"
            >
              Compare
            </button>
          </ng-container>
        </div>
      </div>

      <div class="d-flex justify-content-end align-items-center gap-3">
        <p-multiSelect
          *ngIf="showDropdown()"
          #multiSelectInstallations
          [options]="installations()"
          formControlName="installationId"
          optionLabel="name"
          optionValue="id"
          placeholder="Select"
          [showToggleAll]="!multiSelectInstallations.isEmpty()"
          display="comma"
          [showClear]="!!filterForm.controls.installationId.value?.length"
          styleClass="new-version-multiselect"
          panelStyleClass="new-version-panel"
          [style.width]="'200px'"
        ></p-multiSelect>

        <button
          type="button"
          class="btn-export align-items-center d-flex"
          (click)="exportExcel()"
        >
          <img src="assets/excel.svg" />
          Export Excel File
        </button>
      </div>
    </div>

    <p-divider />

    <div class="mb-10">
      <lha-header-nav [navItems]="navItems()"></lha-header-nav>
    </div>
    <router-outlet />
  </p-card>
</div>
