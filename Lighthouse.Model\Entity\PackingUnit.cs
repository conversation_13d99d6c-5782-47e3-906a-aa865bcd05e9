﻿namespace Lighthouse.Model.Entity
{
    public class PackingUnit
    {
        public PackingUnit()
        {
            PackingUnitId = Guid.NewGuid();
            CreatedDate = DateTime.UtcNow;
            UpdatedDate = DateTime.UtcNow;
        }

        public Guid PackingUnitId { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public Guid CreatedById { get; set; }

        public User CreatedBy { get; set; }

        public Guid? UpdatedById { get; set; }

        public User UpdatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public bool Deleted { get; set; }
    }
}