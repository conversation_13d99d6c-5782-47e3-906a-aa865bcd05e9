<p-dialog [draggable]="false" [closable]="false" [modal]="true"
          [(visible)]="dialogVisible"
          [style]="{ width: '1240px' }">
    <ng-template pTemplate="header">
        <span class="p-dialog-title">
            Hire Request Details
        </span>
    </ng-template>
    <ng-template pTemplate="content">
        <div class="d-flex flex-direction-column h-100">
            <span class="p-dialog-header">Hire Request Info</span>
            <p-divider></p-divider>
            <div class="flex-1 d-flex">
                <div class="flex-1 p-20">
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Description</span>
                        <span class="field-value">{{ hireRequest().cargoDescription }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label  f-bold mr-10">Unit Quantity</span>
                        <span class="flex-1">{{ hireRequest().unitQuantity }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">CCU Supplier</span>
                        <span class="flex-1">{{ hireRequest().vendorVendorName }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Asset</span>
                        <span class="flex-1">{{ hireRequest().assetName }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Requested By</span>
                        <span class="flex-1">{{ hireRequest().requestedBy }}</span>
                    </div>
                </div>
                <div class="flex-1 p-20">
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label_second_column f-bold mr-10">Off Waiting List Date</span>
                        <span class="flex-1">{{ hireRequest().offWaitingListDate | date:'dd/MM/yyyy' }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label_second_column f-bold mr-10">Order Taken By</span>
                        <span class="flex-1">{{ hireRequest().orderTakenBy }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label_second_column f-bold mr-10">Verified Date Time</span>
                        <span class="flex-1">{{ hireRequest().verifiedDateTime | date:'dd/MM/yyyy HH:mm'
                            }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label_second_column f-bold mr-10">Confirmed By</span>
                        <span class="flex-1">{{ hireRequest().confirmedBy }}</span>
                    </div>
                </div>
                <div class="flex-1 p-20 buttons_subsection">
                    <div class="mb-10 d-flex align-items-center">
                        <button
                                class="btn-primary"
                                type="button"
                                (click)="confirmHireRequest()">
                            Confirm
                        </button>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <button
                                class="btn-primary "
                                type="button"
                                (click)="editHireRequest(hireRequest())">
                            Edit
                        </button>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <button
                                class="btn-tertiary mr-8 "
                                type="button"
                                (click)="onCancel($event)">
                            Cancel
                        </button>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <button
                                class="btn-negative-primary mr-8 "
                                type="button"
                                (click)="onDelete($event)">
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-direction-column h-100">
            <span class="p-dialog-header">Additional Requirements</span>
            <p-divider></p-divider>
            <div class="flex-1 d-flex">
                <div class="flex-1 p-20">
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">CST</span>
                        <span class="field-value">
                            <i class="pi pi-check" *ngIf="hireRequest().cstRequired; else falseIcon"></i>
                            <ng-template #falseIcon>
                                <i class="pi pi-times"></i>
                            </ng-template>
                        </span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Rem. Sides</span>
                        <span class="field-value">
                            <i class="pi pi-check" *ngIf="hireRequest().removableSidesRequired; else falseIcon"></i>
                            <ng-template #falseIcon>
                                <i class="pi pi-times"></i>
                            </ng-template>
                        </span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Tarpaulin</span>
                        <span class="field-value">
                            <i class="pi pi-check" *ngIf="hireRequest().tarpaulinRequired; else falseIcon"></i>
                            <ng-template #falseIcon>
                                <i class="pi pi-times"></i>
                            </ng-template>
                        </span>
                    </div>
                </div>
                <div class="flex-1 p-20">
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Doors</span>
                        <span class="field-value">
                            <i class="pi pi-check" *ngIf="hireRequest().doorsRequired; else falseIcon"></i>
                            <ng-template #falseIcon>
                                <i class="pi pi-times"></i>
                            </ng-template>
                        </span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Net</span>
                        <span class="field-value">
                            <i class="pi pi-check" *ngIf="hireRequest().netRequired; else falseIcon"></i>
                            <ng-template #falseIcon>
                                <i class="pi pi-times"></i>
                            </ng-template>
                        </span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Shelves</span>
                        <span class="field-value">
                            <i class="pi pi-check" *ngIf="hireRequest().shelvesRequired; else falseIcon"></i>
                            <ng-template #falseIcon>
                                <i class="pi pi-times"></i>
                            </ng-template>
                        </span>
                    </div>
                </div>
                <div class="flex-1 p-20">
                    <div class="mb-10 d-flex align-items-center">
                        <span class="flex-1">
                            <label class="f-bold" for="comments-box">Comments</label>
                            <textarea
                                      id="comments-box"
                                      rows="3"
                                      cols="30"
                                      style="resize: none;"
                                      readonly
                                      pInputTextarea
                                      [(ngModel)]="hireRequest().comments">
                            </textarea>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-direction-column h-100" *ngIf="isConfirmedHireRequestsList">
            <span class="p-dialog-header">Allocated Units</span>
            <p-divider></p-divider>
            <div class="flex-1 d-flex">
                <div class="flex-2 p-20">
                    <contain-hire-request-allocated-units-table
                                                                *ngIf="hireRequest()?.hireRequestId"
                                                                [hireRequest]="hireRequest()"
                                                                (allocatedUnitsModified)="allocatedUnitsUpdated($event)">
                    </contain-hire-request-allocated-units-table>
                </div>
            </div>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <button class="btn-tertiary" (click)="hideDialog()">Close</button>
    </ng-template>
</p-dialog>

<contain-hire-request-confirm-dialog
                                     *ngIf="confirmDialogVisible && hireRequest()?.hireRequestId"
                                     [dialogVisible]="confirmDialogVisible"
                                     [hireRequestId]="hireRequest().hireRequestId"
                                     [hireRequest]="hireRequest()"
                                     (dialogToggle)="hideConfirmDialog()">
</contain-hire-request-confirm-dialog>

<contain-hire-request-edit-dialog
                                  *ngIf="editDialogVisible && hireRequest()?.hireRequestId"
                                  [dialogVisible]="editDialogVisible"
                                  [hireRequestId]="hireRequest().hireRequestId"
                                  [hireRequest]="hireRequest()"
                                  (dialogToggle)="hideEditDialog()">
</contain-hire-request-edit-dialog>