global using Lighthouse.Data.Context;
global using Lighthouse.Repositories;
global using Lighthouse.Repositories.Interface;
global using System;
global using System.Collections.Generic;
global using System.Threading.Tasks;
global using Lighthouse.Model.Entity;
global using AutoMapper;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Logging;
global using System.Linq;
global using Lighthouse.Email.Interface;
global using Lighthouse.Storage.Interface;
global using Lighthouse.Util.Settings;
global using Microsoft.Extensions.Options;
global using System.IO;
global using System.Security.Claims;
global using Syncfusion.XlsIO;
global using System.Globalization;
global using Lighthouse.Service.Utility;
global using Microsoft.AspNetCore.Http;
global using Lighthouse.Model.Constants;
global using Lighthouse.Model.Enums;
global using TimeZoneConverter;
global using Microsoft.IdentityModel.Tokens;
global using System.Net;
global using System.Net.Mail;
global using Lighthouse.Service.Utility.Interface;
global using ClosedXML.Excel;
global using System.Text.RegularExpressions;
global using Auth0.ManagementApi;
global using System.Net.Http.Headers;
global using System.Net.Http;
global using Common.Extensions;
global using Lighthouse.Cache.Interface;
global using Microsoft.Extensions.DependencyInjection;
global using Lighthouse.Service.Security;
global using Lighthouse.Service.Security.ClaimTransformation;
global using Microsoft.AspNetCore.Authentication;
global using Lighthouse.Model.ViewModel.MasterData.DangerousGoodLocation;
global using System.Net.Http.Json;
global using System.Threading;
global using Lighthouse.Service.Data.Constants;
global using System.ComponentModel;
global using Microsoft.EntityFrameworkCore.Storage;
global using System.Collections.ObjectModel;
global using Microsoft.Extensions.Primitives;
global using StackExchange.Redis;
global using System.Text.Json;
global using Lighthouse.Service.Data.Allocate;
global using Lighthouse.Service.Data.Plan;
global using Lighthouse.Service.Data.MasterData;
global using Lighthouse.Service.Data.Allocate.Interface;
global using Lighthouse.Service.Data.Plan.Interface;
global using Lighthouse.Service.Data.MasterData.Interface;
global using System.Text;
global using Microsoft.Extensions.Configuration;
global using System.Collections.Concurrent;
global using Syncfusion.Pdf;
global using Syncfusion.Drawing;
global using Syncfusion.Pdf.Graphics;
global using Syncfusion.Pdf.Grid;
global using UnitsNet;

//allocate
global using Lighthouse.Model.ViewModel.Allocate.HireStatement;
global using Lighthouse.Model.ViewModel.Allocate.HireStatementBulk;
global using Lighthouse.Model.ViewModel.Allocate.Activity;
global using Lighthouse.Model.ViewModel.Allocate.VesselTank;
global using Lighthouse.Model.ViewModel.Allocate.VesselActivity;
global using Lighthouse.Model.ViewModel.Allocate.BulkTransaction;
global using Lighthouse.Model.ViewModel.Allocate.TankStatus;
global using Lighthouse.Model.ViewModel.Allocate.BillingPeriod;
global using Lighthouse.Model.ViewModel.Allocate.VoyageBillingPeriod;
global using Lighthouse.Model.ViewModel.Allocate.ClientBillingPeriodTimeAllocation;
global using Lighthouse.Model.ViewModel.Allocate.DeckUsage;
global using Lighthouse.Model.ViewModel.Allocate.ParallelActivity;
global using Lighthouse.Model.ViewModel.Allocate.BulkRequest;
global using Lighthouse.Model.ViewModel.Allocate.VoyageBulkQuantities;
global using Lighthouse.Model.ViewModel.Allocate.BillingPeriodDocument;
global using Lighthouse.Model.ViewModel.Allocate.VesselBillingPeriods;
global using Lighthouse.Model.ViewModel.Allocate.Import;
global using Lighthouse.Model.ViewModel.Allocate.ClientBillingPeriod;
global using Lighthouse.Model.ViewModel.Allocate.VoyageBulkConsumedCost;
global using Lighthouse.Model.ViewModel.Allocate.CostAllocation;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail.TransportRequestMaterialDetailSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo.TransportRequestBulkCargoSnapshot;

//plan
global using Lighthouse.Model.ViewModel.Plan.ActivityCategory;
global using Lighthouse.Model.ViewModel.Plan.ActivityCategoryType;
global using Lighthouse.Model.ViewModel.Plan.VoyageCargoSailingRequestActivity;

//masterdata
global using Lighthouse.Model.ViewModel.MasterData.MobileWell;
global using Lighthouse.Model.ViewModel.MasterData.Location;
global using Lighthouse.Model.ViewModel.MasterData.ClientLocation;
global using Lighthouse.Model.ViewModel.MasterData.AssetDistance;
global using Lighthouse.Model.ViewModel.MasterData.AssetLocation;
global using Lighthouse.Model.ViewModel.MasterData.Area;
global using Lighthouse.Model.ViewModel.MasterData.BlockingActivity;
global using Lighthouse.Model.ViewModel.MasterData.AreaBlockingActivity;
global using Lighthouse.Model.ViewModel.MasterData.Site;
global using Lighthouse.Model.ViewModel.MasterData.SailingRequest;
global using Lighthouse.Model.ViewModel.MasterData.User;
global using Lighthouse.Model.ViewModel.MasterData.Vessel;
global using Lighthouse.Model.ViewModel.MasterData.Asset;
global using Lighthouse.Model.ViewModel.MasterData.TankType;
global using Lighthouse.Model.ViewModel.MasterData.Distance;
global using Lighthouse.Model.ViewModel.MasterData.Client;
global using Lighthouse.Model.ViewModel.MasterData.BulkType;
global using Lighthouse.Model.ViewModel.MasterData.Unit;
global using Lighthouse.Model.ViewModel.MasterData.ReportType;
global using Lighthouse.Model.ViewModel.MasterData.ClientReportType;
global using Lighthouse.Model.ViewModel.MasterData.ClientAsset;
global using Lighthouse.Model.ViewModel.MasterData.Voyage;
global using Lighthouse.Model.ViewModel.MasterData.Setting;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.DangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.Cargo;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest;
global using Lighthouse.Model.ViewModel.Contain.CargoFamily;
global using Lighthouse.Model.ViewModel.Contain.CargoType;
global using Lighthouse.Model.ViewModel.Contain.CargoSize;
global using Lighthouse.Model.ViewModel.MasterData.Crane;
global using Lighthouse.Model.ViewModel.MasterData.Vendor;
global using Lighthouse.Model.ViewModel.MasterData.VendorWarehouse;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestAttachment;
global using Lighthouse.Model.ViewModel.MasterData.Employee;
global using Lighthouse.Model.ViewModel.MasterData.SquadEmployee;
global using Lighthouse.Model.ViewModel.MasterData.Squad;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargoSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoAttachment;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo.TransportRequestBulkCargoAttachment;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail.TransportRequestmaterialDetailAttachment;
global using Lighthouse.Model.ViewModel.MasterData.LoadCell;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoBundling;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo.TransportRequestBulkCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.PauseReason;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail.TransportRequestMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestReporting;
global using Lighthouse.Model.ViewModel.MasterData.CargoDescription;
global using Lighthouse.Model.ViewModel.MasterData.WeightCategory;
global using Lighthouse.Model.ViewModel.MasterData.Trailer;
global using Lighthouse.Model.ViewModel.MasterData.Vehicle;
global using Lighthouse.Model.ViewModel.MasterData.District;
global using Lighthouse.Model.ViewModel.MasterData.Driver;
global using Lighthouse.Model.ViewModel.MasterData.Pool;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoLoad;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoBulkDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData;
global using Lighthouse.Model.ViewModel.MasterData.ClusterHistory;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo.VoyageCargoMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo.VoyageMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.Dashboard;
global using Lighthouse.Model.ViewModel.MasterData.PackingUnit;

//flow
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoLift;
global using Lighthouse.Model.ViewModel.Flow.FlowVoyage;
global using Lighthouse.Model.ViewModel.Flow.VoyageEvent;
global using Lighthouse.Model.ViewModel.Flow.VoyageAttachment;
global using Lighthouse.Model.ViewModel.Flow.VoyageMaterialDetail;
global using Lighthouse.Model.ViewModel.Flow.VoyageReservedArea;
global using Lighthouse.Model.ViewModel.Flow.VoyageComment;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoBulk;
global using Lighthouse.Model.ViewModel.Flow.VoyageMaterialDetailSnapshot;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoBulkSnapshot;
global using Lighthouse.Service.Data.Flow.Interface;
global using Lighthouse.Model.ViewModel.Flow.CargoCertificate;
global using Lighthouse.Model.ViewModel.Flow.VoyageInspection;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoInspection;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalkSite;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalkEmployee;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalk;
global using Lighthouse.Model.ViewModel.Flow.VoyageSpecialNote;
global using Lighthouse.Model.ViewModel.Flow.LiftingPlan;
global using Lighthouse.Service.Data.Flow;
global using Lighthouse.Model.ViewModel.Flow.VoyageLiftingJob;

global using Lighthouse.Model.ViewModel.Flow.VoyageCargoInspectionAttachment;
global using Lighthouse.Model.ViewModel.Flow.VoyagePlanningDetail;

global using Lighthouse.Service.Data.Flow.BackgroundServices.Interface;
global using Lighthouse.Service.Data.Flow.BackgroundServices;
global using Lighthouse.Model.ViewModel.Flow.DepartureEmail;
global using Microsoft.Extensions.Hosting;

global using Lighthouse.Service.Data.Flow.ReportStrategies;

global using Lighthouse.Model.ViewModel.Flow.DiscrepancyReportEmail;

//contain
global using Lighthouse.Model.ViewModel.Contain.HireRequest;
global using Lighthouse.Service.Data.Contain;
global using Lighthouse.Service.Data.Contain.Interface;
global using Lighthouse.Model.ViewModel.Contain.HireRequest.Filter;
global using Lighthouse.Model.ViewModel.Contain.HireRequestCargo;
global using Lighthouse.Model.ViewModel.Contain.HireRequestCargoEvent;
global using Lighthouse.Model.ViewModel.Contain.MovementMatching;
global using Lighthouse.Service.Data.Shared;
global using Lighthouse.Model.ViewModel.Contain.CCU;
global using System.ComponentModel.DataAnnotations;
global using System.Reflection;

// Exceptions
global using Lighthouse.Service.Exceptions;
global using System.Linq.Expressions;