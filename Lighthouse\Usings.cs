// System
global using System;
global using System.Collections.Generic;
global using System.ComponentModel.DataAnnotations;
global using System.IO;
global using System.IO.Compression;
global using System.Linq;
global using System.Net;
global using System.Threading.Tasks;
global using System.Security.Claims;
global using System.Diagnostics;
global using System.Globalization;

// Microsoft
global using Microsoft.AspNetCore.Authentication.JwtBearer;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Builder;
global using Microsoft.AspNetCore.Hosting;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.ResponseCompression;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Logging;
global using Microsoft.IdentityModel.Tokens;
global using Microsoft.OpenApi.Models;
global using Microsoft.AspNetCore.Identity;
global using Microsoft.Extensions.Options;
global using Microsoft.ApplicationInsights.DependencyCollector;
global using Microsoft.AspNetCore.Mvc.ModelBinding;

// Azure
global using Azure.Identity;
global using Azure.Security.KeyVault.Secrets;

// JSON
global using Newtonsoft.Json;

// Easy Password Validator
global using Easy_Password_Validator;
global using Easy_Password_Validator.Models;

// Lighthouse Models
global using Lighthouse.Model.Mapping;
global using Lighthouse.Model.Constants;
global using Lighthouse.Model.Utility;

// Lighthouse Services
global using Lighthouse.Service.Data.Allocate.Interface;
global using Lighthouse.Service.Data.Plan.Interface;
global using Lighthouse.Service.Data.MasterData.Interface;
global using Lighthouse.Service.Security;
global using Lighthouse.Service.Extensions;

// Lighthouse Context, Initializer and Settings
global using Lighthouse.Data.Context;
global using Lighthouse.Data.Initializer;
global using Lighthouse.Service.Data.Constants;
global using Lighthouse.Util.Security;
global using Lighthouse.Util.Settings;
global using StackExchange.Redis;
global using Lighthouse.Cache.Extensions;
global using Lighthouse.Extensions;
global using Auth0.ManagementApi.Paging;
global using Lighthouse.TimeZoneConversion;

// Lighthouse Storage
global using Lighthouse.Storage;
global using Lighthouse.Storage.Interface;

// Lighthouse Email
global using Lighthouse.Email;
global using Lighthouse.Email.Interface;

// Lighthouse Middleware
global using Lighthouse.Middleware.Exceptions;
global using Lighthouse.Middleware.Tenant;

//allocate
global using Lighthouse.Model.ViewModel.Allocate.HireStatement;
global using Lighthouse.Model.ViewModel.Allocate.HireStatementBulk;
global using Lighthouse.Model.ViewModel.Allocate.Activity;
global using Lighthouse.Model.ViewModel.Allocate.VesselTank;
global using Lighthouse.Model.ViewModel.Allocate.VesselActivity;
global using Lighthouse.Model.ViewModel.Allocate.BulkTransaction;
global using Lighthouse.Model.ViewModel.Allocate.TankStatus;
global using Lighthouse.Model.ViewModel.Allocate.BillingPeriod;
global using Lighthouse.Model.ViewModel.Allocate.DeckUsage;
global using Lighthouse.Model.ViewModel.Allocate.BulkRequest;
global using Lighthouse.Model.ViewModel.Allocate.VoyageBulkQuantities;
global using Lighthouse.Model.ViewModel.Allocate.BillingPeriodDocument;
global using Lighthouse.Model.ViewModel.Allocate.ClientBillingPeriod;
global using Lighthouse.Model.ViewModel.Allocate.VesselBillingPeriods;
global using Lighthouse.Model.ViewModel.Contain.CargoFamily;
global using Lighthouse.Model.ViewModel.Contain.CargoType;
global using Lighthouse.Model.ViewModel.Contain.CargoSize;
global using Lighthouse.Model.Entity;
//plan
global using Lighthouse.Model.ViewModel.Plan.ActivityCategory;
global using Lighthouse.Model.ViewModel.Plan.ActivityCategoryType;
global using Lighthouse.Model.ViewModel.Plan.VoyageCargoSailingRequestActivity;
//masterdata
global using Lighthouse.Model.ViewModel.MasterData.MobileWell;
global using Lighthouse.Model.ViewModel.MasterData.Location;
global using Lighthouse.Model.ViewModel.MasterData.ClientLocation;
global using Lighthouse.Model.ViewModel.MasterData.AssetLocation;
global using Lighthouse.Model.ViewModel.MasterData.Area;
global using Lighthouse.Model.ViewModel.MasterData.AreaBlockingActivity;
global using Lighthouse.Model.ViewModel.MasterData.BlockingActivity;
global using Lighthouse.Model.ViewModel.MasterData.Site;
global using Lighthouse.Model.ViewModel.MasterData.SailingRequest;
global using Lighthouse.Model.ViewModel.MasterData.Constants;
global using Lighthouse.Model.ViewModel.MasterData.User;
global using Lighthouse.Model.ViewModel.MasterData.Vessel;
global using Lighthouse.Model.ViewModel.MasterData.Asset;
global using Lighthouse.Model.ViewModel.MasterData.TankType;
global using Lighthouse.Model.ViewModel.MasterData.Distance;
global using Lighthouse.Model.ViewModel.MasterData.Client;
global using Lighthouse.Model.ViewModel.MasterData.BulkType;
global using Lighthouse.Model.ViewModel.MasterData.Unit;
global using Lighthouse.Model.ViewModel.MasterData.ReportType;
global using Lighthouse.Model.ViewModel.MasterData.Voyage;
global using Lighthouse.Model.ViewModel.MasterData.Setting;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.DangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.Cargo;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestAttachment;
global using Lighthouse.Model.ViewModel.MasterData.DangerousGoodLocation;
global using Lighthouse.Model.ViewModel.MasterData.Crane;
global using Lighthouse.Model.ViewModel.MasterData.Vendor;
global using Lighthouse.Model.ViewModel.MasterData.VendorWarehouse;
global using Lighthouse.Model.ViewModel.MasterData.Squad;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargoSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoAttachment;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo.TransportRequestBulkCargoAttachment;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail.TransportRequestmaterialDetailAttachment;
global using Lighthouse.Model.ViewModel.MasterData.LoadCell;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoBundling;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo.TransportRequestBulkCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.PauseReason;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestReporting;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail.TransportRequestMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.Employee;
global using Lighthouse.Model.ViewModel.MasterData.CargoDescription;
global using Lighthouse.Model.ViewModel.MasterData.WeightCategory;
global using Lighthouse.Model.ViewModel.MasterData.Trailer;
global using Lighthouse.Model.ViewModel.MasterData.Vehicle;
global using Lighthouse.Model.ViewModel.MasterData.District;
global using Lighthouse.Model.ViewModel.MasterData.Driver;
global using Lighthouse.Model.ViewModel.MasterData.Pool;
global using Lighthouse.Service.Data.Shared.Interface;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoLoad;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoBulkDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData;
global using Lighthouse.Model.ViewModel.MasterData.Dashboard;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo.VoyageCargoMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo.VoyageMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.PackingUnit;

//flow
global using Lighthouse.Service.Data.Flow.Interface;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoLift;
global using Lighthouse.Model.ViewModel.Flow.VoyageReservedArea;
global using Lighthouse.Model.ViewModel.Flow.FlowVoyage;
global using Lighthouse.Model.ViewModel.Flow.VoyageEvent;
global using Lighthouse.Model.ViewModel.Flow.CargoCertificate;
global using Lighthouse.Model.ViewModel.Flow.VoyageComment;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoBulk;
global using Lighthouse.Model.ViewModel.Flow.VoyageMaterialDetail;
global using Lighthouse.Model.ViewModel.Flow.VoyageAttachment;
global using Lighthouse.Model.ViewModel.Flow.VoyageInspection;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoInspection;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoInspectionAttachment;
global using Lighthouse.Service.Data.Flow.Queries;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalk;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalkSite;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalkEmployee;
global using Lighthouse.Model.ViewModel.Flow.VoyagePlanningDetail;
global using Lighthouse.Model.ViewModel.Flow.VoyageSpecialNote;
global using Lighthouse.Model.ViewModel.Flow.LiftingPlan;
global using Lighthouse.Model.ViewModel.Flow.VoyageLiftingJob;
global using Lighthouse.Model.ViewModel.Flow.DepartureEmail;
global using Lighthouse.Service.Data.Flow.ReportStrategies;
global using Lighthouse.Model.ViewModel.Flow.DiscrepancyReportEmail;

//contain
global using Lighthouse.Service.Data.Contain.Interface;
global using Lighthouse.Model.Enums;
global using Lighthouse.Model.ViewModel.Contain.HireRequest;
global using Lighthouse.Model.ViewModel.Contain.HireRequest.Filter;
global using Lighthouse.Model.ViewModel.Contain.HireRequestCargo;
global using Lighthouse.Model.ViewModel.Contain.HireRequestCargoEvent;
global using Lighthouse.Model.ViewModel.Contain.MovementMatching;
global using Lighthouse.Model.ViewModel.Contain.CCU;