<p-dialog
  [draggable]="false"
  [closable]="false"
  header="Edit Cargo Hire"
  [modal]="true"
  [(visible)]="dialogVisible"
  [style]="{ width: '1040px' }"
>
  <ng-template pTemplate="content">
    <div class="cargo-hire-edit-dialog">
      <div class="d-flex flex-direction-column h-100">
        <form class="mt-10" [formGroup]="cargoHireForm" (ngSubmit)="submit()">
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>CCU Supplier</span>
              <p-dropdown
                [options]="vendors()"
                styleClass="new-version"
                [filter]="true"
                formControlName="vendorId"
                optionLabel="vendorName"
                optionValue="vendorId"
                inputId="vendor"
                [showClear]="true"
                appendTo="body"
              />
            </div>
          </div>
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>Unit</span>
              <input
                type="text"
                pInputText
                formControlName="cargoCCUId"
                [readOnly]="true"
                [disabled]="true"
              />
            </div>
          </div>
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>Type</span>
              <input
                type="text"
                pInputText
                formControlName="cargoUnitType"
                [readOnly]="true"
                [disabled]="true"
              />
            </div>
          </div>
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>Client</span>
              <p-dropdown
                [options]="clients()"
                [filter]="true"
                formControlName="clientId"
                styleClass="new-version"
                optionLabel="name"
                optionValue="clientId"
                inputId="client"
                [showClear]="true"
                appendTo="body"
              />
            </div>
          </div>
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>Billing Asset</span>
              <p-dropdown
                [options]="assets()"
                [filter]="true"
                formControlName="billingAssetId"
                styleClass="new-version"
                optionLabel="name"
                optionValue="assetId"
                inputId="billingAsset"
                [showClear]="true"
                appendTo="body"
              />
            </div>
          </div>
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>On-Hired</span>
              <p-calendar
                [inputId]="'onHiredDate'"
                [tabindex]="0"
                [showIcon]="true"
                [showTime]="true"
                [readonlyInput]="false"
                dateFormat="dd/mm/yy"
                formControlName="onHiredDate"
                (keydown)="$event.stopPropagation()"
                appendTo="body"
              >
              </p-calendar>
            </div>
          </div>
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>Vendor-Outbound Date</span>
              <p-calendar
                [inputId]="'vendorOutboundDate'"
                [tabindex]="0"
                [showIcon]="true"
                [showTime]="true"
                dateFormat="dd/mm/yy"
                [readonlyInput]="false"
                formControlName="vendorOutboundDate"
                (keydown)="$event.stopPropagation()"
                appendTo="body"
              >
              </p-calendar>
            </div>
          </div>
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>Shipped</span>
              <p-calendar
                [inputId]="'shipped'"
                [tabindex]="0"
                [showIcon]="true"
                [showTime]="true"
                dateFormat="dd/mm/yy"
                [readonlyInput]="false"
                formControlName="shipped"
                (keydown)="$event.stopPropagation()"
                appendTo="body"
              >
              </p-calendar>
            </div>
          </div>
          <div class="d-flex flex-wrap">
            <div class="flex-column p-10 flex-1">
              <span>Returned</span>
              <p-calendar
                [inputId]="'returned'"
                [tabindex]="0"
                [showIcon]="true"
                [showTime]="true"
                dateFormat="dd/mm/yy"
                [readonlyInput]="false"
                formControlName="returned"
                (keydown)="$event.stopPropagation()"
                appendTo="body"
              >
              </p-calendar>
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Vendor-Inbound Date</span>
              <p-calendar
                [inputId]="'vendorInboundDate'"
                [tabindex]="0"
                [showIcon]="true"
                [showTime]="true"
                dateFormat="dd/mm/yy"
                [readonlyInput]="false"
                formControlName="vendorInboundDate"
                (keydown)="$event.stopPropagation()"
                appendTo="body"
              >
              </p-calendar>
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Off-Hired</span>
              <p-calendar
                [inputId]="'offHiredDate'"
                [tabindex]="0"
                [showIcon]="true"
                [showTime]="true"
                [showOnFocus]="cargoHireForm.get('returned')?.value"
                [readonlyInput]="!cargoHireForm.get('returned')?.value"
                dateFormat="dd/mm/yy"
                formControlName="offHiredDate"
                (keydown)="$event.stopPropagation()"
                [minDate]="minOffHiredDate()"
                appendTo="body"
              >
              </p-calendar>
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Ref No/Well</span>
              <input type="text" pInputText formControlName="reference" />
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Manifest (Out)</span>
              <input type="text" pInputText formControlName="manifestOut" />
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Manifest (In)</span>
              <input type="text" pInputText formControlName="manifestIn" />
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Asset</span>
              <p-dropdown
                [options]="assets()"
                [filter]="true"
                formControlName="assetId"
                styleClass="new-version"
                optionLabel="name"
                optionValue="assetId"
                inputId="asset"
                [showClear]="true"
                appendTo="body"
              />
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Vendor-Outbound</span>
              <input type="text" pInputText formControlName="vendorOutbound" />
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Vendor-Inbound</span>
              <input type="text" pInputText formControlName="vendorInbound" />
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <span>Consignment Number</span>
              <input
                type="text"
                pInputText
                formControlName="consignmentNumber"
              />
            </div>
          </div>
          <div class="d-flex w-50 align-items-center gap-4 ml-10">
          <p-inputSwitch formControlName="isPool" />
          <label class="fs-14">Is Pool</label>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div *ngIf="cargoHireForm.value.isPool" class="flex-column p-10 flex-1">
              <span>Container Pool</span>
              <p-dropdown
                [options]="pools()"
                [filter]="true"
                formControlName="containerPoolId"
                styleClass="new-version"
                optionLabel="name"
                optionValue="poolId"
                inputId="containerPool"
                [showClear]="true"
                appendTo="body"
              />
              <small
            class="validation-control-error"
            *ngIf="cargoHireForm.value.containerPoolId?.invalid && cargoHireForm.controls['containerPoolId']?.touched && cargoHireForm.value.isPool"
          >
            Pool is required.
          </small>
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <p-checkbox
                formControlName="longTermHire"
                inputId="longTermHire"
                [binary]="true"
              ></p-checkbox>
              <label for="longTermHire" class="fs-14 color-dark-gray"
                >Longterm Hire</label
              >
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <p-checkbox
                formControlName="netSupplied"
                inputId="netSupplied"
                [binary]="true"
              ></p-checkbox>
              <label for="netSupplied" class="fs-14 color-dark-gray"
                >Net Supplied</label
              >
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <p-checkbox
                formControlName="netReturned"
                inputId="netReturned"
                [binary]="true"
              ></p-checkbox>
              <label for="netReturned" class="fs-14 color-dark-gray"
                >Net Returned</label
              >
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <p-checkbox
                formControlName="tarpaulinSupplied"
                inputId="tarpaulinSupplied"
                [binary]="true"
              ></p-checkbox>
              <label for="tarpaulinSupplied" class="fs-14 color-dark-gray"
                >Tarpaulin Supplied</label
              >
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <p-checkbox
                formControlName="tarpaulinReturned"
                inputId="tarpaulinReturned"
                [binary]="true"
              ></p-checkbox>
              <label for="tarpaulinReturned" class="fs-14 color-dark-gray"
                >Tarpaulin Returned</label
              >
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <p-checkbox
                formControlName="shelvesSupplied"
                inputId="shelvesSupplied"
                [binary]="true"
              ></p-checkbox>
              <label for="shelvesSupplied" class="fs-14 color-dark-gray"
                >Shelves Supplied</label
              >
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10 flex-1">
              <p-checkbox
                formControlName="shelvesReturned"
                inputId="shelvesReturned"
                [binary]="true"
              ></p-checkbox>
              <label for="shelvesReturned" class="fs-14 color-dark-gray"
                >Shelves Returned</label
              >
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
  <ng-template pTemplate="footer">
    <button class="btn-tertiary" type="button" (click)="hideDialog()">
      Cancel
    </button>
    <button
      class="btn-primary"
      type="button"
      (click)="submit()"
      [disabled]="!cargoHireForm.valid || cargoHireForm.pristine"
    >
      Save Hire
    </button>
    <button class="btn-negative-primary" type="button" (click)="onDelete($event)">
      Delete Hire
    </button>
  </ng-template>
</p-dialog>
