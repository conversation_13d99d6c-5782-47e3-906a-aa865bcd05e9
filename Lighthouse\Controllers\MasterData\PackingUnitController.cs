﻿namespace Lighthouse.Controllers.MasterData
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PackingUnitController : ControllerBase
    {
        private readonly IPackingUnitService unitService;
        private readonly IUserService userService;

        public PackingUnitController(IPackingUnitService unitService,
            IUserService userService)
        {
            this.unitService = unitService;
            this.userService = userService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<PackingUnitModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get([FromQuery] PackingUnitSearchModel model)
        {
            return Ok(await unitService.GetAsync(model));
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PackingUnitModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Get(Guid id)
        {
            return Ok(await unitService.GetPackingUnitByIdAsync(id));
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PackingUnitModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Post([FromBody] PackingUnitUpsertModel model)
        {
            if (model is null) return BadRequest();

            var user = await userService.GetCurrentUser(User);
            var unit = await unitService.PostAsync(model, user.UserId);

            if (unit is null) return BadRequest();

            return Ok(unit);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PackingUnitModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Put(Guid id, [FromBody] PackingUnitUpsertModel model)
        {
            if (model is null) return BadRequest();

            var user = await userService.GetCurrentUser(User);
            var unit = await unitService.PutAsync(id, model, user.UserId);

            if (unit is null) return BadRequest();

            return Ok(unit);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [AuthorizeRoles(UserRoleConstant.Admin, UserRoleConstant.SupportUser)]
        public async Task<IActionResult> Delete(Guid id)
        {
            return (await unitService.DeleteAsync(id)) ? Ok() : BadRequest();
        }
    }
}