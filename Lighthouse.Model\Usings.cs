global using System.Collections.Generic;
global using System.ComponentModel.DataAnnotations;
global using System;
global using System.Linq;
global using Lighthouse.Model.Entity;
global using AutoMapper;
global using Lighthouse.Model.Constants;
global using Lighthouse.Model.Converters;
global using Lighthouse.Model.Enums;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc.ModelBinding;
global using System.Threading.Tasks;
global using Microsoft.AspNetCore.Mvc;
global using System.Text.Json.Serialization;
global using System.Text.Json;
global using UnitsNet;
//allocate
global using Lighthouse.Model.ViewModel.Allocate.HireStatement;
global using Lighthouse.Model.ViewModel.Allocate.HireStatementBulk;
global using Lighthouse.Model.ViewModel.Allocate.Activity;
global using Lighthouse.Model.ViewModel.Allocate.VesselTank;
global using Lighthouse.Model.ViewModel.Allocate.VesselActivity;
global using Lighthouse.Model.ViewModel.Allocate.BulkTransaction;
global using Lighthouse.Model.ViewModel.Allocate.TankStatus;
global using Lighthouse.Model.ViewModel.Allocate.BillingPeriod;
global using Lighthouse.Model.ViewModel.Allocate.VoyageBillingPeriod;
global using Lighthouse.Model.ViewModel.Allocate.ClientBillingPeriodTimeAllocation;
global using Lighthouse.Model.ViewModel.Allocate.DeckUsage;
global using Lighthouse.Model.ViewModel.Allocate.ParallelActivity;
global using Lighthouse.Model.ViewModel.Allocate.BulkRequest;
global using Lighthouse.Model.ViewModel.Allocate.VoyageBulkQuantities;
global using Lighthouse.Model.ViewModel.Allocate.BillingPeriodDocument;
global using System.ComponentModel;

//plan
global using Lighthouse.Model.ViewModel.Plan.ActivityCategory;
global using Lighthouse.Model.ViewModel.Plan.ActivityCategoryType;
global using Lighthouse.Model.ViewModel.Plan.VoyageCargoSailingRequestActivity;
//masterdata
global using Lighthouse.Model.ViewModel.MasterData.DangerousGoodLocation;
global using Lighthouse.Model.ViewModel.MasterData.MobileWell;
global using Lighthouse.Model.ViewModel.MasterData.Location;
global using Lighthouse.Model.ViewModel.MasterData.ClientLocation;
global using Lighthouse.Model.ViewModel.MasterData.AssetLocation;
global using Lighthouse.Model.ViewModel.MasterData.Area;
global using Lighthouse.Model.ViewModel.MasterData.AreaBlockingActivity;
global using Lighthouse.Model.ViewModel.MasterData.BlockingActivity;
global using Lighthouse.Model.ViewModel.MasterData.Site;
global using Lighthouse.Model.ViewModel.MasterData.SailingRequest;
global using Lighthouse.Model.ViewModel.MasterData.User;
global using Lighthouse.Model.ViewModel.MasterData.Vessel;
global using Lighthouse.Model.ViewModel.MasterData.Asset;
global using Lighthouse.Model.ViewModel.MasterData.TankType;
global using Lighthouse.Model.ViewModel.MasterData.Distance;
global using Lighthouse.Model.ViewModel.MasterData.Client;
global using Lighthouse.Model.ViewModel.MasterData.BulkType;
global using Lighthouse.Model.ViewModel.MasterData.Unit;
global using Lighthouse.Model.ViewModel.MasterData.ReportType;
global using Lighthouse.Model.ViewModel.MasterData.ClientReportType;
global using Lighthouse.Model.ViewModel.MasterData.ClientAsset;
global using Lighthouse.Model.ViewModel.MasterData.Voyage;
global using Lighthouse.Model.ViewModel.MasterData.Setting;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.DangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.Cargo;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestAttachment;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoAttachment;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo.TransportRequestBulkCargoAttachment;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargo.TransportRequestCargoBundling;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo.TransportRequestBulkCargoDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail.TransportRequestMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail.TransportRequestmaterialDetailAttachment;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestBulkCargo.TransportRequestBulkCargoSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestCargoSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.TransportRequest.TransportRequestMaterialDetail.TransportRequestMaterialDetailSnapshot;
global using Lighthouse.Model.ViewModel.MasterData.Crane;
global using Lighthouse.Model.ViewModel.MasterData.Vendor;
global using Lighthouse.Model.ViewModel.MasterData.SquadEmployee;
global using Lighthouse.Model.ViewModel.MasterData.Squad;
global using Lighthouse.Model.ViewModel.MasterData.Employee;
global using Lighthouse.Model.ViewModel.MasterData.LoadCell;
global using Lighthouse.Model.ViewModel.MasterData.PauseReason;
global using Lighthouse.Model.ViewModel.MasterData.VendorWarehouse;
global using Lighthouse.Model.ViewModel.MasterData.CargoDescription;
global using Lighthouse.Model.ViewModel.MasterData.WeightCategory;
global using Lighthouse.Model.ViewModel.MasterData.Trailer;
global using Lighthouse.Model.ViewModel.MasterData.Vehicle;
global using Lighthouse.Model.ViewModel.MasterData.Driver;
global using Lighthouse.Model.ViewModel.MasterData.District;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoLift;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoInspection;
global using Lighthouse.Model.ViewModel.MasterData.Pool;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoLoad;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargoBulkDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.ClusterHistory;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo.VoyageCargoMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.VoyageCargo.VoyageMaterialDetailDangerousGood;
global using Lighthouse.Model.ViewModel.MasterData.ClientNameHistory;
global using Lighthouse.Model.ViewModel.MasterData.PackingUnit;

//flow
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoLift;
global using Lighthouse.Model.ViewModel.Flow.FlowVoyage;
global using Lighthouse.Model.ViewModel.Flow.VoyageAttachment;
global using Lighthouse.Model.ViewModel.Flow.VoyageEvent;
global using Lighthouse.Model.ViewModel.Flow.VoyageMaterialDetail;
global using Lighthouse.Model.ViewModel.Flow.VoyageComment;
global using Lighthouse.Model.ViewModel.Flow.CargoCertificate;
global using Lighthouse.Model.ViewModel.Flow.VoyageReservedArea;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoBulk;
global using Lighthouse.Model.ViewModel.Flow.VoyageInspection;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoInspection;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoInspectionAttachment;
global using Lighthouse.Model.ViewModel.Flow.VoyageMaterialDetailSnapshot;
global using Lighthouse.Model.ViewModel.Flow.VoyageCargoBulkSnapshot;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalkSite;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalkEmployee;
global using Lighthouse.Model.ViewModel.Flow.ToolBoxTalk;
global using Lighthouse.Model.ViewModel.Flow.VoyagePlanningDetail;
global using Lighthouse.Model.ViewModel.Flow.VoyageSpecialNote;
global using Lighthouse.Model.ViewModel.Flow.LiftingPlan;
global using Lighthouse.Model.ViewModel.Flow.LiftingPlanEmployee;
global using Lighthouse.Model.ViewModel.Flow.LiftingPlanResource;
global using Lighthouse.Model.ViewModel.Flow.VoyageLiftingJob;

//contain
global using Lighthouse.Model.ViewModel.Contain.CargoFamily;
global using Lighthouse.Model.ViewModel.Contain.CargoSize;
global using Lighthouse.Model.ViewModel.Contain.CargoType;
global using Lighthouse.Model.ViewModel.Contain.HireRequestCargo;
global using Lighthouse.Model.ViewModel.Contain.HireRequest;
global using Lighthouse.Model.ViewModel.Contain.HireRequestCargoEvent;
global using Lighthouse.Model.ViewModel.Contain.MovementMatching;
global using Lighthouse.Model.ViewModel.Contain.CCU;
global using System.ComponentModel.DataAnnotations.Schema;