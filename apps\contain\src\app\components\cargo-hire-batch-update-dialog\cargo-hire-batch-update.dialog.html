<p-dialog [draggable]="false" [closable]="false" header="New CCU Hire Details" [modal]="true"
          [(visible)]="dialogVisible"
          [style]="{ width: '1040px' }">
    <ng-template pTemplate="content">
        <div class="cargo-hire-create-dialog">
            <div class="grid-container">
                <form class="mt-10" [formGroup]="cargoHireForm" (ngSubmit)="submit()">
                    <div class="shipped_info" *ngIf="noHireRequestCargoHasShippedInfo()">
                        <div class="d-flex flex-wrap gap-16">
                            <div class="flex-column p-10 flex-1">
                                <span>Shipped</span>
                                <p-calendar
                                            [inputId]="'shipped'"
                                            [tabindex]="0"
                                            [showIcon]="true"
                                            [showTime]="true"
                                            [readonlyInput]="false"
                                            dateFormat="dd/mm/yy"
                                            formControlName="shipped"
                                            (keydown)="$event.stopPropagation()"
                                            appendTo="body">
                                </p-calendar>
                            </div>
                        </div>
                        <div class="d-flex flex-wrap gap-16">
                            <div class="flex-column p-10 flex-1">
                                <span>Manifest (Out)</span>
                                <input
                                       type="text"                                       
                                       pInputText
                                       formControlName="manifestOut" />
                            </div>
                        </div>
                        <div class="d-flex flex-wrap gap-16">
                            <div class="flex-column p-10 flex-1">
                                <span>Ref No/Well</span>
                                <input
                                       type="text"
                                       pInputText
                                       formControlName="reference" />
                            </div>
                        </div>
                    </div>
                    <div class="returnedInfo" *ngIf="everyHireRequestCargoHasShippedInfo()">
                        <div class="d-flex flex-wrap gap-16">
                            <div class="flex-column p-10 flex-1">
                                <span>Returned</span>
                                <p-calendar
                                            [inputId]="'returned'"
                                            [tabindex]="0"
                                            [showIcon]="true"
                                            [showTime]="true"
                                            dateFormat="dd/mm/yy"
                                            [readonlyInput]="false"
                                            formControlName="returned"
                                            (keydown)="$event.stopPropagation()"
                                            appendTo="body">
                                </p-calendar>
                            </div>
                        </div>
                        <div class="d-flex flex-wrap gap-16">
                            <div class="flex-column p-10 flex-1">
                                <span>Manifest (In)</span>
                                <input
                                       type="text"
                                       pInputText
                                       formControlName="manifestIn" />
                            </div>
                        </div>
                        <div class="d-flex flex-wrap gap-16">
                            <div class="flex-column p-10 flex-1">
                                <span>Vendor-Inbound</span>
                                <input
                                       type="text"
                                       pInputText
                                       formControlName="vendorInbound" />
                            </div>
                        </div>
                        <div class="d-flex flex-wrap gap-16">
                            <div class="flex-column p-10 flex-1">
                                <span>Ref No/Well</span>
                                <input
                                       type="text"
                                       pInputText
                                       formControlName="reference" />
                            </div>
                        </div>
                    </div>
                    <div class="reference_only" *ngIf="someHireRequestCagoesHaveShippedInfo()">
                        <div class="d-flex flex-wrap gap-16">
                            <div class="flex-column p-10 flex-1">
                                <span>Ref No/Well</span>
                                <input
                                       type="text"
                                       pInputText
                                       formControlName="reference" />
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="grid-container mt-20">
                <span class="f-bold">Selected Hires</span>
                <p-divider></p-divider>
                <p-table class="mt-20" [columns]="listColumns()" [value]="selectedHireRequestCargoes" [scrollable]="true">
                    <ng-template pTemplate="header" let-columns>
                        <tr>
                            <th *ngFor="let column of columns" scope="col" [style.min-width.px]="column.width"
                                [style.width.%]="(column.width / tableWidth) * 100">
                                <span>{{ column.name }}</span>
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item>
                        <tr>
                            <td>{{item.cargoCCUId }}</td>
                            <td>{{item.onHiredDate | date:'dd/MM/yyyy' }}</td>
                            <td>{{item.clientName }}</td>
                            <td>{{item.billingAssetName }}</td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <button
                class="btn-tertiary"
                type="button"
                (click)="hideDialog()">
            Cancel
        </button>
        <button
                class="btn-primary"
                type="button"
                (click)="submit()"
                [disabled]="cargoHireForm.pristine || !selectedHireRequestCargoes.length">
            Save
        </button>
    </ng-template>
</p-dialog>