﻿namespace Lighthouse.Service.Data.Flow
{
    public class VoyageMaterialDetailService : IVoyageMaterialDetailService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IVoyageCargoWeightUtility _voyageCargoWeightUtility;
        private readonly IVoyageMaterialDetailDangerousGoodService _voyageMaterialDetailDangerousGoodService;

        public VoyageMaterialDetailService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IUserService userService,
            IVoyageCargoWeightUtility voyageCargoWeightUtility,
            IVoyageMaterialDetailDangerousGoodService voyageMaterialDetailDangerousGoodService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userService = userService;
            _voyageCargoWeightUtility = voyageCargoWeightUtility;
            _voyageMaterialDetailDangerousGoodService = voyageMaterialDetailDangerousGoodService;
        }

        public async Task<VoyageMaterialDetailModelResponse> GetAllByVoyageIdAsync(Guid voyageId)
        {
            var measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitAsync(voyageId);

            var materialDetails = await _unitOfWork.Repository<VoyageMaterialDetail>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Voyage)
                .Include(x => x.VoyageMaterialDetailDangerousGood)
                    .ThenInclude(x => x.VoyageCargoDangerousGood)
                        .ThenInclude(x => x.DangerousGood)
                .Include(x => x.VoyageMaterialDetailDangerousGood)
                    .ThenInclude(x => x.VoyageCargoBulkDangerousGood)
                        .ThenInclude(x => x.DangerousGood)
                .Include(x => x.VoyageCargoBulk)
                    .ThenInclude(x => x.VoyageCargoBulkDangerousGood)
                        .ThenInclude(x => x.DangerousGood)
                .Include(x => x.VoyageCargo).ThenInclude(x => x.Asset)
                .Include(x => x.VoyageCargoBulk).ThenInclude(x => x.Asset)
                .Include(x => x.VoyageCargoBulk).ThenInclude(x => x.BulkType)
                .Include(x => x.Vendor)
                .Where(x => x.VoyageId == voyageId)
                .OrderBy(x => x.RowNumber)
                .ToListAsync();

            foreach (var materialDetail in materialDetails)
            {
                materialDetail.WeightKg = (double)_voyageCargoWeightUtility.ConvertWeight(
                    materialDetail.WeightKg,
                    measurementUnit,
                    true);
            }

            var materialDetailsResult = _mapper.Map<List<VoyageMaterialDetailModel>>(materialDetails);

            return new VoyageMaterialDetailModelResponse
            {
                MaterialDetails = materialDetailsResult,
                MeasurementUnit = measurementUnit.ToString()
            };
        }

        public async Task<VoyageMaterialDetailModel> GetByIdAsync(Guid id)
        {
            var voyage = await _unitOfWork.Repository<VoyageMaterialDetail>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Voyage)
                .Include(x => x.VoyageMaterialDetailDangerousGood)
                .Include(x => x.VoyageCargo)
                .Include(x => x.VoyageCargoBulk)
                .Include(x => x.VoyageCargo)
                    .ThenInclude(x => x.Asset)
                .Include(x => x.VoyageCargoBulk)
                    .ThenInclude(x => x.Asset)
                .Include(x => x.VoyageCargoBulk)
                    .ThenInclude(x => x.BulkType)
                .Where(x => x.VoyageMaterialDetailId == id)
                .SingleOrDefaultAsync();
            return _mapper.Map<VoyageMaterialDetailModel>(voyage);
        }

        public async Task<VoyageMaterialDetailModel> CreateAsync(VoyageMaterialDetailUpsertModel model, ClaimsPrincipal user)
        {
            await _unitOfWork.BeginTransactionAsync();

            var voyageMaterialDetail = _mapper.Map<VoyageMaterialDetail>(model);
            voyageMaterialDetail.CreatedById = (await _userService.GetCurrentUser(user)).UserId;
            voyageMaterialDetail.CreatedDate = DateTime.UtcNow;

            var latestRowNumber = await _unitOfWork.Repository<VoyageMaterialDetail>()
                .Query(x => x.VoyageId == model.VoyageId)
                .MaxAsync(x => (int?)x.RowNumber) ?? 0;

            voyageMaterialDetail.RowNumber = ++latestRowNumber;

            voyageMaterialDetail.Status = MaterialDetailStatus.Draft;

            if (voyageMaterialDetail.VoyageCargoBulkId.HasValue && voyageMaterialDetail.VoyageCargoId.HasValue)
                throw new Exception("Only one of VoyageCargoBulk or VoyageCargo can have value");
            if (!voyageMaterialDetail.VoyageCargoBulkId.HasValue && !voyageMaterialDetail.VoyageCargoId.HasValue)
                throw new Exception("Any of VoyageCargoBulk or VoyageCargo should have value");

            voyageMaterialDetail = await _unitOfWork.Repository<VoyageMaterialDetail>().CreateAsync(voyageMaterialDetail);

            await _unitOfWork.SaveChangesAsync();

            var mdDangerousGood = new VoyageMaterialDetailDangerousGoodModel();

            if (voyageMaterialDetail.VoyageCargoBulkId != null)
            {
                var bulkCargo = await GetVoyageCargoBulk(voyageMaterialDetail.VoyageCargoBulkId.Value);
                var materialDetailDangerousGood = new VoyageMaterialDetailDangerousGoodUpsertModel()
                {
                    VoyageBulkCargoDangerousGoodId = bulkCargo.VoyageCargoBulkDangerousGoodId,
                    VoyageCargoDangerousGoodId = null
                };
                await _voyageMaterialDetailDangerousGoodService.AssignMdDgAsync(voyageMaterialDetail.VoyageMaterialDetailId, materialDetailDangerousGood);
            }

            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(voyageMaterialDetail.VoyageMaterialDetailId);
        }

        public async Task<VoyageMaterialDetailModel> UpdateAsync(Guid id, VoyageMaterialDetailUpsertModel model, ClaimsPrincipal user)
        {
            await _unitOfWork.BeginTransactionAsync();

            var currentUser = await _userService.GetCurrentUser(user);

            if (model.VoyageCargoBulkId.HasValue && model.VoyageCargoId.HasValue)
                throw new Exception("Only one of VoyageCargoBulk or VoyageCargo can have value");
            if (!model.VoyageCargoBulkId.HasValue && !model.VoyageCargoId.HasValue)
                throw new Exception("Any of VoyageCargoBulk or VoyageCargo should have value");

            var voyageRequestMaterialDetail = await _unitOfWork.Repository<VoyageMaterialDetail>()
                .Query()
                .AsSplitQuery()
                .SingleOrDefaultAsync(x => x.VoyageMaterialDetailId == id);

            VoyageMaterialDetailDangerousGoodModel mdDangerousGood = null;
            VoyageMaterialDetailModel updatedMaterialDetail = null;

            if (voyageRequestMaterialDetail.VoyageCargoId != model.VoyageCargoId && model.VoyageCargoBulkId != null ||
                model.VoyageCargoBulkId != voyageRequestMaterialDetail.VoyageCargoBulkId)
            {
                if (voyageRequestMaterialDetail.VoyageMaterialDetailDangerousGood != null)
                {
                    updatedMaterialDetail = await _voyageMaterialDetailDangerousGoodService.DeleteAsync(voyageRequestMaterialDetail.VoyageMaterialDetailId);
                }

                if (model.VoyageCargoId == null && model.VoyageCargoBulkId.HasValue)
                {
                    var bulkCargo = await GetVoyageCargoBulk(model.VoyageCargoBulkId.Value);
                    if (bulkCargo.VoyageCargoBulkDangerousGoodId != null)
                    {
                        var materialDetailDangerousGood = new VoyageMaterialDetailDangerousGoodUpsertModel
                        {
                            VoyageBulkCargoDangerousGoodId = bulkCargo.VoyageCargoBulkDangerousGoodId,
                            VoyageCargoDangerousGoodId = null
                        };

                        mdDangerousGood = await _voyageMaterialDetailDangerousGoodService.AssignMdDgAsync(
                            voyageRequestMaterialDetail.VoyageMaterialDetailId,
                            materialDetailDangerousGood
                        );
                    }
                }
            }

            voyageRequestMaterialDetail = _mapper.Map(model, voyageRequestMaterialDetail);

            voyageRequestMaterialDetail.VoyageMaterialDetailDangerousGoodId = mdDangerousGood?.VoyageMaterialDetailDangerousGoodId ??
                (updatedMaterialDetail?.VoyageMaterialDetailId != Guid.Empty ? null : voyageRequestMaterialDetail.VoyageMaterialDetailDangerousGoodId);

            voyageRequestMaterialDetail.UpdatedById = currentUser.UserId;
            voyageRequestMaterialDetail.UpdatedDate = DateTime.UtcNow;

            _unitOfWork.Repository<VoyageMaterialDetail>().Update(voyageRequestMaterialDetail);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(voyageRequestMaterialDetail.VoyageMaterialDetailId);
        }

        public async Task<List<VoyageMaterialDetailModel>> BulkUpdateAsync(Guid voyageId, List<VoyageMaterialDetailUpsertModel> finalTotalList)
        {
            if (finalTotalList.Where(x => x.VoyageMaterialDetailId.HasValue).Count() != finalTotalList.Where(x => x.VoyageMaterialDetailId.HasValue).DistinctBy(x => x.VoyageMaterialDetailId).Count())
                throw new Exception("Duplicate Entry in the list");

            await _unitOfWork.BeginTransactionAsync();

            var currentVoyageMaterialDetailes = await _unitOfWork.Repository<VoyageMaterialDetail>().Query(x => x.VoyageId == voyageId)
                .ToListAsync();

            var measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitAsync(voyageId);

            var itemsToDelete = currentVoyageMaterialDetailes
                .Where(x => !finalTotalList
                    .Any(l => l.VoyageMaterialDetailId == x.VoyageMaterialDetailId))
                .ToList();
            foreach (var voyageMaterialDetailToDelete in itemsToDelete)
            {
                voyageMaterialDetailToDelete.Deleted = true;
                currentVoyageMaterialDetailes.Remove(voyageMaterialDetailToDelete);
            }
            await _unitOfWork.SaveChangesAsync();

            var currentUser = await _userService.GetCurrentUser();

            var listToCreate = new List<VoyageMaterialDetail>();

            foreach (var voyageMaterialDetailToUpsert in finalTotalList)
            {
                voyageMaterialDetailToUpsert.Weight = (double)_voyageCargoWeightUtility.ConvertWeight(voyageMaterialDetailToUpsert.Weight, measurementUnit, false);

                if (voyageMaterialDetailToUpsert.VoyageCargoBulkId.HasValue && voyageMaterialDetailToUpsert.VoyageCargoId.HasValue)
                    throw new Exception("Only one of VoyageCargoBulk or VoyageCargo can have value");
                if (!voyageMaterialDetailToUpsert.VoyageCargoBulkId.HasValue && !voyageMaterialDetailToUpsert.VoyageCargoId.HasValue)
                    throw new Exception("Any of VoyageCargoBulk or VoyageCargo should have value");

                if (voyageMaterialDetailToUpsert.VoyageMaterialDetailId.HasValue) //if it's update
                {
                    bool shouldBeDraft = false;
                    var voyageMaterialDetail = currentVoyageMaterialDetailes.SingleOrDefault(x => x.VoyageMaterialDetailId == voyageMaterialDetailToUpsert.VoyageMaterialDetailId);

                    if (voyageMaterialDetail == null)
                        throw new Exception($"Some voyage cargo material detail items do not belong to this voyage or do not exist");

                    if ((voyageMaterialDetail.Status == MaterialDetailStatus.Submitted)
                    && (voyageMaterialDetailToUpsert.Comments != voyageMaterialDetail.Comments
                    || voyageMaterialDetailToUpsert.DestinationLocation != voyageMaterialDetail.DestinationLocation
                    || voyageMaterialDetailToUpsert.PONo != voyageMaterialDetail.PONo
                    || voyageMaterialDetailToUpsert.Requester != voyageMaterialDetail.Requester
                    || voyageMaterialDetailToUpsert.TransportRequest != voyageMaterialDetail.TransportRequest
                    || voyageMaterialDetailToUpsert.WHSStatus != voyageMaterialDetail.WHSStatus
                    || voyageMaterialDetailToUpsert.CustomStatus != voyageMaterialDetail.CustomStatus
                    || voyageMaterialDetailToUpsert.CustomsEntryType != voyageMaterialDetail.CustomsEntryType
                    || voyageMaterialDetailToUpsert.DocumentNo != voyageMaterialDetail.DocumentNo
                    || voyageMaterialDetailToUpsert.SerialNo != voyageMaterialDetail.SerialNo
                    || voyageMaterialDetailToUpsert.Category != voyageMaterialDetail.Category
                    || voyageMaterialDetailToUpsert.ProperShippingName != voyageMaterialDetail.ProperShippingName
                    || voyageMaterialDetailToUpsert.ManifestNo != voyageMaterialDetail.ManifestNo
                    || voyageMaterialDetailToUpsert.CollectDate != voyageMaterialDetail.CollectDate
                    || voyageMaterialDetailToUpsert.CollectTime != voyageMaterialDetail.CollectTime
                    || voyageMaterialDetailToUpsert.Phone != voyageMaterialDetail.Phone
                    || voyageMaterialDetailToUpsert.PackingUnitId != voyageMaterialDetail.PackingUnit?.PackingUnitId
                    || voyageMaterialDetailToUpsert.MaterialDescription != voyageMaterialDetail.MaterialDescription
                    || voyageMaterialDetailToUpsert.VendorId != voyageMaterialDetail.VendorId
                    || voyageMaterialDetailToUpsert.POTransport != voyageMaterialDetail.POTransport
                    || voyageMaterialDetailToUpsert.Quantity != voyageMaterialDetail.Quantity
                    )) shouldBeDraft = true;

                    _mapper.Map(voyageMaterialDetailToUpsert, voyageMaterialDetail);

                    if (shouldBeDraft) voyageMaterialDetail.Status = MaterialDetailStatus.Draft;

                    voyageMaterialDetail.WeightKg = voyageMaterialDetailToUpsert.Weight;
                    voyageMaterialDetail.UpdatedById = currentUser.UserId;
                    voyageMaterialDetail.UpdatedDate = DateTime.UtcNow;
                }
                else //if it's create
                {
                    var voyageMaterialDetail = _mapper.Map<VoyageMaterialDetail>(voyageMaterialDetailToUpsert);

                    _mapper.Map(voyageMaterialDetailToUpsert, voyageMaterialDetail);

                    voyageMaterialDetail.VoyageMaterialDetailId = Guid.NewGuid();
                    voyageMaterialDetail.CreatedById = currentUser.UserId;
                    voyageMaterialDetail.CreatedDate = DateTime.UtcNow;

                    voyageMaterialDetail.RowNumber = listToCreate.Any() ?
                                                (listToCreate.Max(x => x.RowNumber) + 1) :
                                                currentVoyageMaterialDetailes.Any() ?
                                                    (currentVoyageMaterialDetailes.Max(x => x.RowNumber) + 1) : 1;

                    voyageMaterialDetail.Status = MaterialDetailStatus.Draft;
                    voyageMaterialDetail.WeightKg = voyageMaterialDetailToUpsert.Weight;

                    listToCreate.Add(voyageMaterialDetail);
                }
            }

            await _unitOfWork.SaveChangesAsync(); //this applies the updates

            await _unitOfWork.Repository<VoyageMaterialDetail>().BulkCreateAsync(listToCreate);

            await _unitOfWork.SaveChangesAsync(); //this applies the creates

            await _unitOfWork.CommitAsync();

            var allByVoyageId = await GetAllByVoyageIdAsync(voyageId);

            return allByVoyageId.MaterialDetails;
        }

        public async Task BulkCancelAsync(List<Guid> ids)
        {
            await _unitOfWork.Repository<VoyageMaterialDetail>().Query(x => ids.Contains(x.VoyageMaterialDetailId))
                .ExecuteUpdateAsync(x => x.SetProperty(p => p.IsCancelled, true));
        }

        public async Task BulkDeleteAsync(List<Guid> ids)
        {
            await _unitOfWork.Repository<VoyageMaterialDetail>()
               .Query(x => ids.Contains(x.VoyageMaterialDetailId))
               .ExecuteUpdateAsync(setters => setters
                   .SetProperty(x => x.UpdatedDate, DateTime.UtcNow)
                   .SetProperty(x => x.Deleted, true)
               );

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var materialDetail = await _unitOfWork.Repository<VoyageMaterialDetail>().GetAsync(id);
            materialDetail.Deleted = true;
            await _unitOfWork.SaveChangesAsync();
        }

        private async Task<VoyageCargoBulk> GetVoyageCargoBulk(Guid voyageCargoBulkId)
        {
            return await _unitOfWork.Repository<VoyageCargoBulk>()
                .Query()
                .Where(x => x.VoyageCargoBulkId == voyageCargoBulkId)
                .SingleOrDefaultAsync();
        }
    }
}
