﻿namespace Lighthouse.Service.Data.MasterData {
    public class ClusterService : IClusterService {

        private readonly IUnitOfWork _unitOfWork;

        public ClusterService(IUnitOfWork unitOfWork) {
            _unitOfWork = unitOfWork;
        }
        public async Task<List<ClusterModel>> GetAllClustersAsync() {
            var assets = await _unitOfWork.Repository<Asset>()
                .Query()
                .Select(a => new AssetModel {
                    AssetId = a.AssetId,
                    Name = a.Name,
                    AssetType = a.AssetType,
                    ClientId = a.ClientId.Value,
                    ClientName = a.Client.Name,
                    ClientAssets = a.ClientAssets.Select(c => new ClientAssetModel {
                        AssetName = c.Asset.Name,
                        AssetId = c.AssetId,
                        ClientId = c.ClientId,
                        ClientName = c.Client.Name,
                        StartDateTime = c.StartDateTime,
                        EndDateTime = c.EndDateTime
                    }).ToList(),
                    AssetLocations = a.AssetLocations.Select(l => new AssetLocationModel {
                        AssetId = l.AssetId,
                        LocationId = l.LocationId,
                        LocationName = l.Location.Name
                    }).ToList(),
                })
                .ToListAsync();

            var assetsById = assets.ToDictionary(a => a.AssetId);

            var clusterChildren = await _unitOfWork.Repository<Asset>()
                .Query()
                .Where(a => a.ClusterHeadId != null)
                .Select(a => new {
                    ParentId = a.ClusterHeadId.Value,
                    Child = new AssetModel {
                        AssetId = a.AssetId,
                        Name = a.Name,
                        ClientId = a.ClientId.Value,
                        ClientName = a.Client.Name
                    }
                })
                .ToListAsync();

            var clusters = clusterChildren
                .GroupBy(x => x.ParentId)
                .Where(g => assetsById.ContainsKey(g.Key))
                .Select(g => new ClusterModel {
                    AssetId = assetsById[g.Key].AssetId,
                    AssetName = assetsById[g.Key].Name,
                    AssetType = assetsById[g.Key].AssetType,
                    ClientId = assetsById[g.Key].ClientId.Value,
                    ClientName = assetsById[g.Key].ClientName,
                    ClusterChildren = g.Select(x => x.Child).ToList(),
                    ClientAssets = assetsById[g.Key].ClientAssets.ToList(),
                    AssetLocations = assetsById[g.Key].AssetLocations.ToList(),
                })
                .ToList();

            return clusters;
        }

        public async Task<List<ClusterHistoryModel>> GetAllClusterHistoryForClusters(Guid clusterHeadId) {
            var clusterChildren = await _unitOfWork.Repository<ClusterHistory>()
                .Query()
                .Where(x => x.ClusterHeadId == clusterHeadId)
                .Select(x => new ClusterHistoryModel {
                    ClusterChildId = x.ClusterChildId,
                    ClusterChildName = x.ClusterChild.Name,
                    ClusterHeadId = x.ClusterHeadId,
                    ClusterHeadName = x.ClusterHead.Name,
                    StartDateTime = x.StartDateTime,
                    EndDateTime = x.EndDateTime,
                })
                .OrderByDescending(x => x.StartDateTime)
                .ToListAsync();

            return clusterChildren;
        }

        public async Task<List<ClusterHistoryModel>> GetAllClusterHistoryForAsset(Guid assetId) {
            var clusterHistory = await _unitOfWork.Repository<ClusterHistory>()
                .Query()
                .Where(x => x.ClusterChildId == assetId)
                .Select(x => new ClusterHistoryModel {
                    ClusterHistoryId = x.ClusterHistoryId,
                    ClusterChildId = x.ClusterChildId,
                    ClusterChildName = x.ClusterChild.Name,
                    ClusterHeadId = x.ClusterHeadId,
                    ClusterHeadName = x.ClusterHead.Name,
                    StartDateTime = x.StartDateTime,
                    EndDateTime = x.EndDateTime,
                })
                .OrderByDescending(x => x.StartDateTime)
                .ToListAsync();
            return clusterHistory;
        }

        public async Task CreateClusterHistoryAsync(AssetUpsertModel model, Guid assetId, Guid userId) {
            var clusterHistory = new ClusterHistory {
                ClusterHistoryId = Guid.NewGuid(),
                ClusterChildId = assetId, 
                ClusterHeadId = model.ClusterHeadId.Value, 
                StartDateTime = model.StartDateTime.Value, 
                EndDateTime = model.EndDateTime,
                CreatedById = userId,
                UpdatedById = userId,
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow
            };

            var clusterAsset = await _unitOfWork.Repository<Asset>()
                .Query(x => x.AssetId == model.ClusterHeadId)
                .FirstOrDefaultAsync();

            var clientAssets = await _unitOfWork.Repository<ClientAsset>()
                .Query(x => x.AssetId == clusterAsset.AssetId && x.ClientId == model.ClientId.Value)
                .Where(x => (x.StartDateTime > DateTime.UtcNow || (x.StartDateTime <= DateTime.UtcNow && (x.EndDateTime == null || x.EndDateTime > DateTime.UtcNow))) && !x.IsClusterHead)
                .ExecuteUpdateAsync(x => x.SetProperty(y => y.IsClusterHead, true));

            await _unitOfWork.Repository<ClusterHistory>().CreateAsync(clusterHistory);
        }
    }
}
