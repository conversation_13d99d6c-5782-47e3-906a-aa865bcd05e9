namespace Lighthouse.Service.Extensions
{
    public static class ServiceExtensions
    {
        public static void AddLighthouseServices(this IServiceCollection services)
        {
            //dataservices
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IVesselService, VesselService>();
            services.AddScoped<IAssetService, AssetService>();
            services.AddScoped<ITankTypeService, TankTypeService>();
            services.AddScoped<IDistanceService, DistanceService>();
            services.AddScoped<IClientService, ClientService>();
            services.AddScoped<IHireStatementService, HireStatementService>();
            services.AddScoped<IActivityService, ActivityService>();
            services.AddScoped<IBulkTypeService, BulkTypeService>();
            services.AddScoped<IUnitService, UnitService>();
            services.AddScoped<IVesselTankService, VesselTankService>();
            services.AddScoped<IReportTypeService, ReportTypeService>();
            services.AddScoped<IVoyageService, VoyageService>();
            services.AddScoped<IClientAssetService, ClientAssetService>();
            services.AddScoped<IClientReportTypeService, ClientReportTypeService>();
            services.AddScoped<IBulkTransactionService, BulkTransactionService>();
            services.AddScoped<IVesselActivityService, VesselActivityService>();
            services.AddScoped<ITankStatusService, TankStatusService>();
            services.AddScoped<IBillingPeriodService, BillingPeriodService>();
            services.AddScoped<IVoyageBillingPeriodService, VoyageBillingPeriodService>();
            services.AddScoped<ICostAllocationService, CostAllocationService>();
            services.AddScoped<IBulkTransactionCostProcessorService, BulkTransactionCostProcessorService>();
            services.AddScoped<ICharterRateProcessorService, CharterRateProcessorService>();
            services.AddScoped<IClientBillingPeriodService, ClientBillingPeriodService>();
            services.AddScoped<IVesselBillingPeriodService, VesselBillingPeriodService>();
            services.AddScoped<IDeckUsageService, DeckUsageService>();
            services.AddScoped<IHireStatementBulkService, HireStatementBulkService>();
            services.AddScoped<IVoyageBulkService, VoyageBulkService>();
            services.AddScoped<IParallelActivityService, ParallelActivityService>();
            services.AddScoped<IBulkRequestService, BulkRequestService>();
            services.AddScoped<ISettingService, SettingService>();
            services.AddScoped<IVoyageSailingTimeCostAllocationProcessorService, VoyageSailingTimeCostAllocationProcessorService>();
            services.AddScoped<IMobileWellService, MobileWellService>();
            services.AddScoped<IVesselActivityValidatorService, VesselActivityValidatorService>();
            services.AddScoped<IHireStatementVoyageService, HireStatementVoyageService>();
            services.AddScoped<IVoyageBulkQuantitiesService, VoyageBulkQuantitiesService>();
            services.AddScoped<IExportService, ExportService>();
            services.AddScoped<IDeckUsageTimeCostProcessorService, DeckUsageTimeCostProcessorService>();
            services.AddScoped<IClientBillingPeriodTimeAllocationService, ClientBillingPeriodTimeAllocationService>();
            services.AddScoped<IVoyageTimeCostAllocationService, VoyageTimeCostAllocationService>();
            services.AddScoped<IBillingPeriodDocumentService, BillingPeriodDocumentService>();
            services.AddScoped<IVoyageImportService, VoyageImportService>();
            services.AddScoped<IVesselActivityImportService, VesselActivityImportService>();
            services.AddScoped<IBulkTransactionImportService, BulkTransactionImportService>();
            services.AddScoped<ITankStatusImportService, TankStatusImportService>();
            services.AddScoped<IDeckUsageImportService, DeckUsageImportService>();
            services.AddScoped<IBulkRequestImportService, BulkRequestImportService>();
            services.AddScoped<ISailingRequestService, SailingRequestService>();
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<ILocationService, LocationService>();
            services.AddScoped<IClientLocationService, ClientLocationService>();
            services.AddScoped<IAssetLocationService, AssetLocationService>();
            services.AddScoped<IActvityCategoryService, ActivityCategoryService>();
            services.AddScoped<IActivityCategoryTypeService, ActivityCategoryTypeService>();
            services.AddScoped<IAuth0UserService, Auth0UserService>();
            services.AddScoped<IAreaService, AreaService>();
            services.AddScoped<ISiteService, SiteService>();
            services.AddScoped<ICargoService, CargoService>();
            services.AddScoped<IVoyageCargoService, VoyageCargoService>();
            services.AddScoped<IVoyageCargoLiftService, VoyageCargoLiftService>();
            services.AddScoped<IVoyageCargoSailingRequestActivityService, VoyageCargoSailingRequestActivityService>();
            services.AddScoped<ISailingRequestActivityService, SailingRequestActivityService>();
            services.AddScoped<IFlowVoyageService, FlowVoyageService>();
            services.AddScoped<IVoyageEventService, VoyageEventService>();
            services.AddScoped<IDangerousGoodService, DangerousGoodService>();
            services.AddScoped<IDangerousGoodLocationService, DangerousGoodLocationService>();
            services.AddScoped<IVoyageCargoDangerousGoodService, VoyageCargoDangerousGoodService>();
            services.AddScoped<IVendorService, VendorService>();
            services.AddScoped<IVendorWarehouseService, VendorWarehouseService>();
            services.AddScoped<IAreaBlockingActivityService, AreaBlockingActivityService>();
            services.AddScoped<IBlockingActivityService, BlockingActivityService>();
            services.AddScoped<ITransportRequestCargoService, TransportRequestCargoService>();
            services.AddScoped<ITransportRequestMaterialDetailService, TransportRequestMaterialDetailService>();
            services.AddScoped<ITransportRequestBulkCargoService, TransportRequestBulkCargoService>();
            services.AddScoped<ITransportRequestAttachmentService, TransportRequestAttachmentService>();
            services.AddScoped<ITransportRequestCargoAttachmentService, TransportRequestCargoAttachmentService>();
            services.AddScoped<ITransportRequestCargoAttachmentService, TransportRequestCargoAttachmentService>();
            services.AddScoped<ITransportRequestBulkCargoAttachmentService, TransportRequestBulkCargoAttachmentService>();
            services.AddScoped<ITransportRequestMaterialDetailAttachmentService, TransportRequestMaterialDetailAttachmentService>();
            services.AddScoped<ITransportRequestService, TransportRequestService>();
            services.AddScoped<ICargoCertificateService, CargoCertificateService>();
            services.AddScoped<IVoyageCommentService, VoyageCommentService>();
            services.AddScoped<IVoyageCargoBulkService, VoyageCargoBulkService>();
            services.AddScoped<ICargoFamilyService, CargoFamilyService>();
            services.AddScoped<ICargoTypeService, CargoTypeService>();
            services.AddScoped<ICargoSizeService, CargoSizeService>();
            services.AddScoped<IVoyageMaterialDetailService, VoyageMaterialDetailService>();
            services.AddScoped<IVoyageInspectionService, VoyageInspectionService>();
            services.AddScoped<IVoyageMaterialDetailSnapshotService, VoyageMaterialDetailSnapshotService>();
            services.AddScoped<IVoyageCargoBulkSnapshotService, VoyageCargoBulkSnapshotService>();
            services.AddScoped<IVoyageCargoInspectionService, VoyageCargoInspectionService>();
            services.AddScoped<IVoyageCargoSnapshotService, VoyageCargoSnapshotService>();
            services.AddScoped<IVoyageAttachmentService, VoyageAttachmentService>();
            services.AddScoped<IHireRequestService, HireRequestService>(); 
            services.AddScoped<ICraneService, CraneService>(); 
            services.AddScoped<IVoyageReservedAreaService, VoyageReservedAreaService>();
            services.AddScoped<ISquadService, SquadService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<ISquadEmployeeService, SquadEmployeeService>();
            services.AddScoped<IToolBoxTalkService, ToolBoxTalkService>();
            services.AddScoped<IToolBoxTalkEmployeeService, ToolBoxTalkEmployeeService>();
            services.AddScoped<IToolBoxTalkSiteService, ToolBoxTalkSiteService>();
            services.AddScoped<IVoyagePlanningDetailService, VoyagePlanningDetailService>();
            services.AddScoped<IHireRequestCargoService, HireRequestCargoService>();
            services.AddScoped<IVoyageSpecialNoteService, VoyageSpecialNoteService>();
            services.AddScoped<IVoyageCargoInspectionAttachmentService, VoyageCargoInspectionAttachmentService>();
            services.AddScoped<ILoadCellService, LoadCellService>();
            services.AddScoped<ILiftingPlanService, LiftingPlanService>();
            services.AddScoped<ITransportRequestSnapshotService, TransportRequestSnapshotService>();
            services.AddScoped<ITransportRequestCargoDangerousGoodService, TransportRequestCargoDangerousGoodService>();
            services.AddScoped<ITransportRequestCargoBundlingService, TransportRequestCargoBundlingService>();
            services.AddScoped<ITransportRequestBulkCargoDangerousGoodService, TransportRequestBulkCargoDangerousGoodService>();
            services.AddScoped<IPauseReasonService, PauseReasonService>();
            services.AddScoped<IVoyageLiftingJobService, VoyageLiftingJobService>();
            services.AddScoped<ITransportRequestMaterialDetailDangerousGoodService, TransportRequestMaterialDetailDangerousGoodService>();
            services.AddScoped<IVoyageOffshoreLocationService, VoyageOffshoreLocationService>();
            services.AddScoped<ICargoDescriptionService, CargoDescriptionService>();
            services.AddScoped<IWeightCategoryService, WeightCategoryService>();
            services.AddScoped<ITrailerService, TrailerService>();
            services.AddScoped<IDistrictService, DistrictService>();
            services.AddScoped<IVoyageCargoWeightUtility, VoyageCargoWeightUtility>();
            services.AddScoped<IDangerousGoodsUtility, DangerousGoodsUtility>();
            services.AddScoped<IDateTimeService, DateTimeService>();
            services.AddScoped<IRequestToFlowDataSyncService, RequestToFlowDataSyncService>();
            services.AddScoped<ITimeZoneConversionService, TimeZoneConversionService>();
            services.AddScoped<IVehicleService, VehicleService>();
            services.AddScoped<IDriverService, DriverService>();
            services.AddScoped<IToolBoxTalkExpirationService, ToolBoxTalkExpirationService>();
            services.AddScoped<IHireRequestCargoEventService, HireRequestCargoEventService>();
            services.AddScoped<IMovementMatchingService, MovementMatchingService>();
            services.AddScoped<ITransportRequestReportingService, TransportRequestReportingService>();
            services.AddScoped<ISubmissionSnapshotExtensionService, SubmissionSnapshotExtensionService>();
            services.AddScoped<IVoyageCargoLoadService, VoyageCargoLoadService>();
            services.AddScoped<IVoyageCargoReportService, VoyageCargoReportService>();
            services.AddScoped<IVoyageMaterialDetailDangerousGoodService, VoyageMaterialDetailDangerousGoodService>();
            services.AddScoped<IPoolService, PoolService>();
            services.AddScoped<ICargoEventService, CargoEventService>();
            services.AddScoped<IFlowVoyageCargoBulkDangerousGoodService, FlowVoyageCargoBulkDangerousGoodService>();
            services.AddScoped<IClusterService, ClusterService>();
            services.AddScoped<IDashboardService, DashboardService>();
            services.AddScoped<IPackingUnitService, PackingUnitService>();

            // unit of work
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            //security
            services.AddSingleton<Auth0ManagementApiClientFactory>();
            services.AddScoped<IClaimsTransformation, RolesClaimsTransformation>();

            //background
            services.AddHostedService<ToolBoxTalkExpirationBackgroundService>();

            //report documents
            services.AddScoped<IDictionary<ReportDocumentType, IReportStrategy>>(provider => new Dictionary<ReportDocumentType, IReportStrategy>
            {
                { ReportDocumentType.DeckPlan, provider.GetRequiredService<DeckPlanReportStrategy>() },
                { ReportDocumentType.Discrepancy, provider.GetRequiredService<DiscrepancyReportStrategy>() },
            });
        }
    }
}
