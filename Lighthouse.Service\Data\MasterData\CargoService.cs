﻿namespace Lighthouse.Service.Data.MasterData {
    public class CargoService : ICargoService {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IServiceProvider serviceProvider;

        public CargoService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IUserService userService,
            IServiceProvider serviceProvider) {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userService = userService;
            this.serviceProvider = serviceProvider;
        }

        public async Task<List<CargoModel>> GetAllAsync(MaintenanceCargoFilterModel filterModel) {
            List<Guid> vendorGuids = [];
            if (filterModel.VendorIds != null && filterModel.VendorIds.Count != 0 && filterModel.VendorIds.All(x => !string.IsNullOrEmpty(x))) {
                vendorGuids = filterModel.VendorIds.Select(Guid.Parse).ToList();
            }

            List<Guid> familyGuids = [];
            if (filterModel.FamilyIds != null && filterModel.FamilyIds.Count != 0) {
                familyGuids = filterModel.FamilyIds.Select(Guid.Parse).ToList();
            }

            var cargos = new List<Cargo>();

            if (!string.IsNullOrEmpty(filterModel.SearchTerm)) {
                cargos = await _unitOfWork.Repository<Cargo>()
                            .Query(x => !x.Deleted &&
                                (vendorGuids.Count == 0 || (x.VendorId.HasValue && vendorGuids.Contains(x.VendorId.Value))) &&
                                (familyGuids.Count == 0 || (x.FamilyId.HasValue && familyGuids.Contains(x.FamilyId.Value))) &&
                                (filterModel.Status == "All" || (filterModel.Status == "Pending" ? !x.IsApproved : x.IsApproved)) &&
                                    (x.CCUId.Contains(filterModel.SearchTerm) ||
                                    x.Location.Name.Contains(filterModel.SearchTerm) ||
                                    x.CargoDescription.Description.Contains(filterModel.SearchTerm) ||
                                    (
                                        (x.CargoSize.Name ?? "") + " " +
                                        (x.CargoType.Name ?? "") + " " +
                                        (x.CargoFamily.Name ?? "")
                                    ).Contains(filterModel.SearchTerm))
                            )
                            .AsNoTracking()
                            .AsSplitQuery()
                            .Include(x => x.CargoFamily)
                            .Include(x => x.CargoSize)
                            .Include(x => x.CargoType)
                            .Include(x => x.Vendor)
                            .Include(x => x.Location)
                            .Include(x => x.Pool)
                            .Include(x => x.CargoDescription)
                            .IgnoreQueryFilters()
                            .OrderByDescending(o => o.CreatedDate).ToListAsync();
            } else {
                cargos = await _unitOfWork.Repository<Cargo>()
                             .Query(x => !x.Deleted &&
                                 (vendorGuids.Count == 0 || (x.VendorId.HasValue && vendorGuids.Contains(x.VendorId.Value))) &&
                                 (familyGuids.Count == 0 || (x.FamilyId.HasValue && familyGuids.Contains(x.FamilyId.Value))) &&
                                 (filterModel.Status == "All" || (filterModel.Status == "Pending" ? !x.IsApproved : x.IsApproved))
                             )
                             .AsNoTracking()
                             .AsSplitQuery()
                             .Include(x => x.CargoFamily)
                             .Include(x => x.CargoSize)
                             .Include(x => x.CargoType)
                             .Include(x => x.Vendor)
                             .Include(x => x.Location)
                             .Include(x => x.Pool)
                             .Include(x => x.CargoDescription)
                             .IgnoreQueryFilters()
                             .OrderByDescending(o => o.CreatedDate)
                             .ToListAsync();
            }

            //remove any item from cargos list that IsAdhoc and which is not currently on hire
            cargos = cargos.Where(c => !c.IsAdhoc || c.CcuHireStatus == CargoHireStatus.OnHire).ToList();

            return _mapper.Map<List<CargoModel>>(cargos);
        }

        public async Task<List<CargoModel>> GetAllAsyncIncludingAdhocForContain(UserModel user)
        {
            var cargos = new List<Cargo>();
            var locationId = user.LocationId;

            cargos = await _unitOfWork.Repository<Cargo>()
                             .Query(x => !x.Deleted && !x.Disabled && x.LocationId == locationId)
                             .AsNoTracking()
                             .AsSplitQuery()
                             .Include(x => x.CargoFamily)
                             .Include(x => x.CargoSize)
                             .Include(x => x.CargoType)
                             .Include(x => x.Vendor)
                             .Include(x => x.Location)
                             .Include(x => x.Pool)
                             .Include(x => x.CargoDescription)
                             .IgnoreQueryFilters()
                             .OrderByDescending(o => o.CreatedDate)
                             .ToListAsync();

            return _mapper.Map<List<CargoModel>>(cargos);
        }

        public async Task<List<CargoModel>> GetAllCCUsAsync(ClaimsPrincipal user, CcuFilter filter, bool isContain = false) {
            var currentUser = await _userService.GetCurrentUser(user);
            var locationId = currentUser.LocationId;

            var cargosQuery = _unitOfWork.Repository<Cargo>()
                .Query(x =>
                    (
                        (string.IsNullOrWhiteSpace(filter.SearchTerm) || x.CCUId.ToLower().Contains(filter.SearchTerm.ToLower())) ||
                        (string.IsNullOrWhiteSpace(filter.SearchTerm) || (x.PoolId.HasValue && x.Pool.Name.ToLower().Contains(filter.SearchTerm.ToLower())))
                    ) &&
                    (!filter.IsPool || (x.IsPool && x.PoolId.HasValue)) &&
                    (!filter.ShowDisabled ? !x.Disabled : true) &&
                    !x.Deleted &&
                    (isContain && !filter.IncludeOtherSites ? (!locationId.HasValue || x.LocationId == locationId) : true) &&
                    (isContain && !filter.ShowDisabled ? !x.Disabled : true))
                .AsNoTracking()
                .AsSplitQuery()
                .Include(w => w.CargoSize)
                .Include(w => w.CargoType)
                .Include(w => w.CargoFamily)
                .Include(w => w.Vendor)
                .Include(x => x.Location)
                .Include(x => x.Pool)
                .Include(x => x.CargoDescription)
                .IgnoreQueryFilters();

            if (filter.VendorIds != null && filter.VendorIds.Length > 0) {
                var vendorGuids = filter.VendorIds.Select(Guid.Parse).ToList();
                cargosQuery = cargosQuery.Where(x => x.VendorId.HasValue && vendorGuids.Contains(x.VendorId.Value));
            }

            var cargos = await cargosQuery
                .OrderByDescending(o => o.CreatedDate)
                .Select(s => _mapper.Map<CargoModel>(s))
                .ToListAsync();

            foreach (var cargo in cargos) {
                bool condition = !cargo.IsApproved && cargo.LocationId != locationId;

                cargo.CanBeActionedInContainCcuList = !condition;

                cargo.HireRequestCargoId = await GetHireRequestCargoIdByCargoId(cargo.CargoId);
                cargo.IsCargoHireCreated = cargo.HireRequestCargoId.HasValue && cargo.HireRequestCargoId != Guid.Empty;
            }

            return cargos;
        }

        public async Task<Guid?> GetHireRequestCargoIdByCargoId(Guid cargoId) {
            var hireRequestCargoId = await _unitOfWork.Repository<HireRequestCargo>()
                .Query(q =>
                    q.CargoId == cargoId &&
                    !q.Deleted &&
                    !q.OffHiredDate.HasValue &&
                    q.IsHired == true &&
                    q.Cargo.CcuHireStatus == CargoHireStatus.OnHire)
                .IgnoreQueryFilters()
                .OrderByDescending(o => o.CreatedDate)
                .Select(x => x.HireRequestCargoId)
                .FirstOrDefaultAsync();

            return hireRequestCargoId == Guid.Empty ? (Guid?)null : hireRequestCargoId;
        }

        public async Task<CargoModel> EnableDisableCargo(Guid cargoId, ClaimsPrincipal user) {
            var cargo = await _unitOfWork.Repository<Cargo>()
                .Query(q => q.CargoId == cargoId && !q.Deleted)
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync();

            var userModel = await _userService.GetCurrentUser(user);

            if (cargo is null) {
                throw new Exception("Cargo not found");
            }

            var isOnHire = await CheckIfCargoIsOnHire(cargo.CargoId);
            var usedInFlowOrRequest = await CheckIfCargoIsUsedInFlowOrRequest(cargo.CargoId);

            if (await CheckIfCargoIsOnHire(cargo.CargoId) || await CheckIfCargoIsUsedInFlowOrRequest(cargo.CargoId) || cargo.CcuHireStatus == CargoHireStatus.OnHire) {
                throw new Exception($"Cargo {(isOnHire ? "is on hire" : "is currently in use")} and cannot be {(cargo.Disabled ? "enabled" : "disabled")} at this time");
            }

            cargo.Disabled = !cargo.Disabled;
            _unitOfWork.Repository<Cargo>().Update(cargo);

            //create cargo event for enabled/disabled
            await _unitOfWork.Repository<CargoEvent>().CreateAsync(new CargoEvent
            {
                CargoId = cargo.CargoId,
                EventType = cargo.Disabled ? CargoEventType.CargoDisabled : CargoEventType.CargoEnabled,
                Details = cargo.Disabled ? "Disabled" : "Enabled",
                Deleted = false,
                CreatedDate = DateTime.UtcNow,
                EventDate = DateTime.UtcNow,
                CreatedById = userModel.UserId,
                UpdatedById = userModel.UserId,
                UpdatedDate = DateTime.UtcNow
            });

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<CargoModel>(cargo);
        }

        public async Task<List<CargoModel>> GetAllCargoesByTRAsync(Guid transportRequestId) {
            var loggedInUser = await _userService.GetCurrentUser();

            List<CargoModel> oneOffTransportRequestCargoes = await _unitOfWork.Repository<TransportRequestCargo>()
                .Query()
                .Where(x => x.TransportRequest.TransportRequestId == transportRequestId && x.CargoId == null)
                .AsNoTracking()
                .Select(x => new CargoModel {
                    CargoId = x.TransportRequestCargoId,
                    CcuId = x.OneOffCcuId,
                    IsOneOff = true,
                })
                .OrderBy(x => x.CcuId)
                .ToListAsync();

            List<CargoModel> cargoes = _mapper.Map<List<CargoModel>>(await _unitOfWork.Repository<Cargo>()
                .Query(q => q.IsApproved && q.LocationId == loggedInUser.LocationId && !q.Deleted)
                .AsSplitQuery()
                .Include(w => w.CargoFamily)
                .Include(w => w.CargoSize)
                .Include(w => w.CargoType)
                .Include(w => w.Vendor)
                .Include(x => x.Location)
                .AsNoTracking()
                .IgnoreQueryFilters()
                .OrderBy(x => x.CCUId)
                .ToListAsync());

            cargoes.AddRange(oneOffTransportRequestCargoes);
            return cargoes;
        }

        public async Task<List<CargoModel>> GetAllApprovedAsync() {
            var cargos = await _unitOfWork.Repository<Cargo>()
                .Query(q => q.IsApproved && !q.Deleted)
                .AsSplitQuery()
                .Include(w => w.CargoFamily)
                .Include(w => w.CargoSize)
                .Include(w => w.CargoType)
                .Include(w => w.Vendor)
                .Include(x => x.Location)
                .AsNoTracking()
                .IgnoreQueryFilters()
                .OrderBy(x => x.CCUId)
                .ToListAsync();

            return _mapper.Map<List<CargoModel>>(cargos);
        }

        public async Task<List<CargoModel>> GetAllByCCUAsync(Guid locationId, string ccuIdPart) {
            var cargos = await _unitOfWork.Repository<Cargo>()
                .Query(x => x.LocationId == locationId && x.CCUId.Contains(ccuIdPart) && !x.Deleted && !x.IsAdhoc)
                .AsSplitQuery()
                .Include(x => x.CargoType)
                .Include(x => x.Vendor)
                .IgnoreQueryFilters()
                .OrderBy(x => x.CCUId)
                .ToListAsync();
            return _mapper.Map<List<CargoModel>>(cargos);
        }

        public async Task<bool> ValidateForExtraAsync(Guid voyageId, string ccuId) {
            var ccuIdIsValid = !await _unitOfWork.Repository<Cargo>()
                .Query()
                .IgnoreQueryFilters()
                .AnyAsync(x => x.VoyageCargoes.Any(x =>
                                x.Voyage.VoyageId == voyageId &&
                                !x.IsCancelled &&
                                !x.IsBumped &&
                                !x.Deleted &&
                                x.Status == VoyageCargoStatus.Submitted) &&
                            x.CCUId == ccuId);
            return ccuIdIsValid;
        }

        public async Task<List<CargoModel>> GetAllByLocationAsync(Guid locationId) {
            var cargos = await _unitOfWork.Repository<Cargo>()
                .Query(x => x.LocationId == locationId && !x.Deleted)
                .AsSplitQuery()
                .Include(x => x.CargoType)
                .Include(x => x.CargoFamily)
                .Include(x => x.CargoSize)
                .Include(x => x.Vendor)
                .IgnoreQueryFilters()
                .OrderBy(x => x.CCUId)
                .ToListAsync();

            return _mapper.Map<List<CargoModel>>(cargos);
        }

        public async Task<CargoModel> GetByIdAsync(Guid id) {
            var cargo = await _unitOfWork.Repository<Cargo>()
                .Query(x => x.CargoId == id && !x.Deleted)
                .AsSplitQuery()
                .Include(w => w.CargoFamily)
                .Include(w => w.CargoSize)
                .Include(w => w.CargoType)
                .Include(w => w.Vendor)
                .Include(x => x.Location)
                .Include(x => x.Pool)
                .Include(x => x.CargoDescription)
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync();

            return _mapper.Map<CargoModel>(cargo);
        }

        public async Task<CargoModel> GetByCcuIdAsync(string ccuId) {
            var cargo = await _unitOfWork.Repository<Cargo>()
                .Query(x => x.CCUId == ccuId && !x.Deleted)
                .Include(x => x.Vendor)
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync();
            return _mapper.Map<CargoModel>(cargo);
        }

        public async Task<CargoModel> CreateAsync(CargoUpsertModel upsertModel, ClaimsPrincipal user) {
            if (upsertModel.LocationId == Guid.Empty) {
                throw new Exception("Selected Location is not valid");
            }

            upsertModel.LengthUnit = LengthUnits.mm;
            upsertModel.WeightUnit = WeightUnits.kg;

            // Compute thresholds (100 ft → 30480 mm; 40 ft → 12192 mm)
            var maxLengthMm = Length.From(100, UnitsNet.Units.LengthUnit.Foot).Millimeters;
            var maxWidthMm = Length.From(40, UnitsNet.Units.LengthUnit.Foot).Millimeters;

            // Validate Length
            if (upsertModel.Length > maxLengthMm)
                throw new Exception($"Length too long. Max 100ft ({maxLengthMm}mm).");

            // Validate Width
            if (upsertModel.Width > maxWidthMm)
                throw new Exception($"Width too wide. Max 40ft ({maxWidthMm}mm).");

            if (await HasDuplicates(upsertModel)) {
                throw new Exception("A CCU with this ID, Location, and Owner already exists. Please provide a unique combination.");
            }

            var cargo = _mapper.Map<Cargo>(upsertModel);

            if (cargo.IsPool && !cargo.PoolId.HasValue) {
                throw new Exception("Required to select one value if the 'Pool' is enabled");
            }

            if (upsertModel.Height.HasValue)
                cargo.HeightMm = Length.From(upsertModel.Height.Value, Length.ParseUnit(upsertModel.LengthUnit.ToString())).Millimeters;

            cargo.WidthMm = Length.From(upsertModel.Width, Length.ParseUnit(upsertModel.LengthUnit.ToString())).Millimeters;

            cargo.LengthMm = Length.From(upsertModel.Length, Length.ParseUnit(upsertModel.LengthUnit.ToString())).Millimeters;

            if (upsertModel.TareMass.HasValue)
                cargo.TareMassKg = Mass.From(upsertModel.TareMass.Value, Mass.ParseUnit(upsertModel.WeightUnit.ToString())).Kilograms;

            if (upsertModel.MaxGrossWeight.HasValue)
                cargo.MaxGrossWeightKg = Mass.From(upsertModel.MaxGrossWeight.Value, Mass.ParseUnit(upsertModel.WeightUnit.ToString())).Kilograms;

            var creator = await _userService.GetCurrentUser(user);

            cargo.CreatedDate = DateTime.UtcNow;
            cargo.CreatedById = creator.UserId;
            cargo.CargoStatus = null;

            cargo = await _unitOfWork.Repository<Cargo>().CreateAsync(cargo);
            
            //create cargo event for created
            await _unitOfWork.Repository<CargoEvent>().CreateAsync(new CargoEvent
            {
                CargoId = cargo.CargoId,
                EventType = CargoEventType.CargoCreated,
                Details = "Created",
                Deleted = false,
                CreatedDate = DateTime.UtcNow,
                EventDate = DateTime.UtcNow,
                CreatedById = creator.UserId,
                UpdatedDate = DateTime.UtcNow,
                UpdatedById = creator.UserId,
            });

            //create cargo event for first owner
            await _unitOfWork.Repository<CargoEvent>().CreateAsync(new CargoEvent
            {
                CargoId = cargo.CargoId,
                EventType = CargoEventType.CargoOwnerChanged,
                Details = "Owner Changed",
                Deleted = false,
                CreatedDate = DateTime.UtcNow,
                EventDate = DateTime.UtcNow,
                CreatedById = creator.UserId,
                CargoOwnerStartDate = DateTime.UtcNow,
                VendorId = upsertModel.VendorId,
                UpdatedDate = DateTime.UtcNow,
                UpdatedById = creator.UserId,
            });

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<CargoModel>(cargo);
        }

        private async Task<bool> CheckIfCargoIsOnHire(Guid cargoId) {
            //this method checks if the cargo that is being updated is on hire, if so, prevent updating it.
            return await _unitOfWork.Repository<HireRequestCargo>()
                .Query(q =>
                         q.CargoId == cargoId &&
                        !q.Deleted &&
                         q.IsHired == true &&
                        !q.OffHiredDate.HasValue &&
                         q.Cargo.CcuHireStatus == CargoHireStatus.OnHire)
                .AsNoTracking()
                .IgnoreQueryFilters()
                .Include(x => x.Cargo)
                .AnyAsync();
        }

        private async Task<bool> CheckIfCargoIsUsedInFlowOrRequest(Guid cargoId) {
            //this method checks if the cargo that is being updated is used in either the Flow or Request app, if so, prevent updating it.
            var isUsedInFlow = await _unitOfWork.Repository<VoyageCargo>()
                .Query(q => q.CargoId == cargoId && !q.Deleted && q.Voyage.VoyageStatus != VoyageStatus.Complete)
                .Include(x => x.Voyage)
                .IgnoreQueryFilters()
                .AnyAsync();

            var isUsedInRequest = await _unitOfWork.Repository<TransportRequestCargo>()
                .Query(q => q.CargoId == cargoId && !q.Deleted && !q.TransportRequest.IsComplete)
                .Include(x => x.TransportRequest)
                .IgnoreQueryFilters()
                .AnyAsync();

            return isUsedInFlow || isUsedInRequest;
        }

        public async Task<CargoModel> UpdateAsync(Guid id, CargoUpsertModel upsertModel, ClaimsPrincipal user) {
            upsertModel.LengthUnit = LengthUnits.mm;
            upsertModel.WeightUnit = WeightUnits.kg;

            // Compute thresholds (100 ft → 30480 mm; 40 ft → 12192 mm)
            var maxLengthMm = Length.From(100, UnitsNet.Units.LengthUnit.Foot).Millimeters;
            var maxWidthMm = Length.From(40, UnitsNet.Units.LengthUnit.Foot).Millimeters;

            // Validate Length
            if (upsertModel.Length > maxLengthMm)
                throw new Exception($"Length too long. Max 100ft ({maxLengthMm}mm).");

            // Validate Width
            if (upsertModel.Width > maxWidthMm)
                throw new Exception($"Width too wide. Max 40ft ({maxWidthMm}mm).");

            if (await CheckIfCargoIsOnHire(id)) {
                throw new Exception("Cargo is on hire and cannot be updated at this time");
            }

            if (upsertModel.LocationId == Guid.Empty) {
                throw new Exception("Selected Location is not valid");
            }

            var cargo = await _unitOfWork.Repository<Cargo>().Query(q => q.CargoId == id && !q.Deleted).IgnoreQueryFilters().FirstOrDefaultAsync();

            if (await HasDuplicates(upsertModel, id)) {
                throw new Exception($"Cargo with combination of Vendor name and Cargo ID {upsertModel.CCUId} already exists.");
            }

            if (cargo == null)
                throw new Exception("Cargo not found");

            if (cargo.CcuHireStatus == CargoHireStatus.OnHire) {
                throw new Exception("Cargo is on hire and cannot be updated at this time");
            }

            var vendorId = cargo.VendorId;

            _mapper.Map(upsertModel, cargo);

            if (upsertModel.Height.HasValue)
                cargo.HeightMm = Length.From(upsertModel.Height.Value, Length.ParseUnit(upsertModel.LengthUnit.ToString())).Millimeters;

            cargo.WidthMm = Length.From(upsertModel.Width, Length.ParseUnit(upsertModel.LengthUnit.ToString())).Millimeters;

            cargo.LengthMm = Length.From(upsertModel.Length, Length.ParseUnit(upsertModel.LengthUnit.ToString())).Millimeters;

            if (upsertModel.TareMass.HasValue)
                cargo.TareMassKg = Mass.From(upsertModel.TareMass.Value, Mass.ParseUnit(upsertModel.WeightUnit.ToString())).Kilograms;

            if (upsertModel.MaxGrossWeight.HasValue)
                cargo.MaxGrossWeightKg = Mass.From(upsertModel.MaxGrossWeight.Value, Mass.ParseUnit(upsertModel.WeightUnit.ToString())).Kilograms;

            cargo.UpdatedDate = DateTime.UtcNow;
            var userModel = await _userService.GetCurrentUser(user);
            cargo.UpdatedById = userModel.UserId;
                        
            if(vendorId.HasValue && vendorId != upsertModel.VendorId)
            {
                //grab latest cargo event of event type CargoOwnerChanged, if any, and put its owner end date as current date
                var previousOwnerChangedEvent = await _unitOfWork.Repository<CargoEvent>()
                    .Query(q => q.EventType == CargoEventType.CargoOwnerChanged)
                    .OrderByDescending(o => o.CreatedDate)
                    .FirstOrDefaultAsync();

                if(previousOwnerChangedEvent != null)
                {
                    previousOwnerChangedEvent.CargoOwnerEndDate = DateTime.UtcNow;
                    previousOwnerChangedEvent.UpdatedDate = DateTime.UtcNow;
                    previousOwnerChangedEvent.UpdatedById = userModel.UserId;
                    _unitOfWork.Repository<CargoEvent>().Update(previousOwnerChangedEvent);
                }

                //create new cargo event for owner changed
                await _unitOfWork.Repository<CargoEvent>().CreateAsync(new CargoEvent
                {
                    CargoId = cargo.CargoId,
                    EventType = CargoEventType.CargoOwnerChanged,
                    Details = "Owner Changed",
                    Deleted = false,
                    CreatedDate = DateTime.UtcNow,
                    EventDate = DateTime.UtcNow,
                    CreatedById = userModel.UserId,
                    CargoOwnerStartDate = DateTime.UtcNow,
                    VendorId = upsertModel.VendorId,
                    UpdatedDate = DateTime.UtcNow,
                    UpdatedById = userModel.UserId,
                });
            }

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<CargoModel>(cargo);
        }

        public async Task DeleteAsync(Guid id) {
            var cargo = await _unitOfWork.Repository<Cargo>().Query(q => q.CargoId == id).IgnoreQueryFilters().FirstOrDefaultAsync();

            if (cargo == null)
                throw new Exception("Cargo not found");

            if (await CheckIfCargoIsOnHire(cargo.CargoId) || cargo.CcuHireStatus == CargoHireStatus.OnHire) {
                throw new Exception("Cargo is on hire and cannot be deleted at this time");
            }

            cargo.Deleted = true;
            await _unitOfWork.SaveChangesAsync();
        }

        private async Task<bool> HasDuplicates(CargoUpsertModel upsertModel, Guid? id = null) {
            return await _unitOfWork.Repository<Cargo>()
                .Query(x => !x.Deleted &&
                             x.CCUId == upsertModel.CCUId &&
                             x.LocationId == upsertModel.LocationId &&
                             x.VendorId == upsertModel.VendorId &&
                             (!id.HasValue || x.CargoId != id))
                .IgnoreQueryFilters()
                .AnyAsync();
        }

        public async Task ApproveCargo(Guid id, ClaimsPrincipal user) {
            var cargo = await _unitOfWork.Repository<Cargo>()
                .Query(q => q.CargoId == id && !q.Deleted)
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync();

            var userModel = await _userService.GetCurrentUser(user);

            if (cargo == null)
                throw new Exception("Cargo not found");

            _ = Task.Run(async () => {
                using var scope = serviceProvider.CreateScope();
                var scopedUnitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                var voyageCargoes = await scopedUnitOfWork.Repository<VoyageCargo>()
                    .Query()
                    .AsSplitQuery()
                    .Include(x => x.Voyage)
                        .ThenInclude(x => x.Client)
                    .Where(x =>
                        cargo.CCUId == x.CcuId &&
                        x.Voyage.VoyageStatus != VoyageStatus.Complete &&
                        x.Status == VoyageCargoStatus.Submitted &&
                        x.TransportStatus != VoyageCargoTransportStatus.Dispatchable &&
                        x.CargoBackloadStatus == CargoBackloadStatus.Discharged &&
                        (x.CustomsEntryType == VoyageCargoCustomsEntryType.Full ||
                        x.CustomsEntryType == VoyageCargoCustomsEntryType.Mixed))
                    .ToListAsync();

                if (voyageCargoes.Any()) {
                    foreach (var voyageCargo in voyageCargoes) {
                        voyageCargo.TransportStatus = VoyageCargoTransportStatus.Dispatchable;
                        scopedUnitOfWork.Repository<VoyageCargo>().Update(voyageCargo);
                    }

                    await scopedUnitOfWork.SaveChangesAsync();
                }
            });

            cargo.IsApproved = true;

            //create cargo event for event type Approved
            await _unitOfWork.Repository<CargoEvent>().CreateAsync(new CargoEvent
            {
                CargoId = cargo.CargoId,
                EventType = CargoEventType.CargoApproved,
                Details = "Approved",
                Deleted = false,
                CreatedDate = DateTime.UtcNow,
                EventDate = DateTime.UtcNow,
                CreatedById = userModel.UserId,
                UpdatedDate = DateTime.UtcNow,
                UpdatedById = userModel.UserId,
            });

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<List<CargoUnitTypeModel>> GetCargoDescriptions() {
            var dscriptions = await _unitOfWork.Repository<Cargo>()
                .Query(q => !q.Deleted)
                .AsSplitQuery()
                .Include(x => x.CargoSize)
                .Include(x => x.CargoType)
                .Include(x => x.CargoFamily)
                .IgnoreQueryFilters()
                .Select(x => new CargoUnitTypeModel {
                    CargoId = x.CargoId,
                    UnitType = $"{x.CargoSize.Name} {x.CargoType.Name} {x.CargoFamily.Name}"
                })
                .ToListAsync();

            return dscriptions.DistinctBy(x => x.UnitType).ToList();
        }

        public async Task<CargoModel> SetCcuStatusAsync(Guid id, int status, ClaimsPrincipal user) {
            var creator = await _userService.GetCurrentUser(user);

            var cargo = await _unitOfWork.Repository<Cargo>()
                .Query(q => q.CargoId == id && !q.Deleted)
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync()
                ?? throw new Exception("Cargo not found");

            var isShippedStatus = await _unitOfWork.Repository<HireRequestCargoEvent>()
                .Query(q => !q.Deleted && !q.HireRequestCargo.Deleted)
                .AsNoTracking()
                .AsSplitQuery()
                .IgnoreQueryFilters()
                .Include(x => x.HireRequestCargo)
                .Where(x => x.HireRequestCargo.CargoId == id)
                .AnyAsync(x => x.EventType == HireRequestCargoEventType.Shipped);

            var isOnHire = await CheckIfCargoIsOnHire(id);
            var invalidStatuses = new[]
            {
                CargoStatus.UnavailableInspection,
                CargoStatus.UnavailableRepair,
                CargoStatus.UnavailableRejected
            };

            if (isOnHire && isShippedStatus && invalidStatuses.Contains((CargoStatus)status)) {
                throw new Exception($"The status cannot be set to {GetEnumDisplayName((CargoStatus)status)} as the CCU is On Hired and Shipped.");
            }

            if (!Enum.IsDefined(typeof(CargoStatus), status)) {
                throw new Exception("Invalid cargo status value");
            }

            cargo.CargoStatus = (CargoStatus)status;
            cargo.UpdatedDate = DateTime.UtcNow;
            cargo.UpdatedById = (await _userService.GetCurrentUser(user)).UserId;

            await _unitOfWork.Repository<CargoEvent>().CreateAsync(new CargoEvent {
                CargoId = id,
                EventType = CargoEventType.CargoStatusChanged,
                Details = GetEnumDisplayName(cargo.CargoStatus),
                Deleted = false,
                CreatedDate = DateTime.UtcNow,
                EventDate = DateTime.UtcNow,
                CreatedById = creator.UserId,
                UpdatedDate = DateTime.UtcNow,
                UpdatedById = creator.UserId,
            });

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<CargoModel>(cargo);
        }

        private static string GetEnumDisplayName(Enum enumValue) {
            return enumValue.GetType()
                .GetMember(enumValue.ToString())
                .First()
                .GetCustomAttribute<DisplayAttribute>()?
                .Name ?? enumValue.ToString();
        }

        public async Task SetCertificateTestDate(CertificateTestDateModel model) {
            var user = await _userService.GetCurrentUser();

            var cargo = await _unitOfWork.Repository<Cargo>()
                .Query(q => q.CargoId == model.CargoId && !q.Deleted)
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync();

            if (cargo == null) {
                throw new Exception("Cargo not found");
            }

            cargo.CertificateTestDate = model.CertificateTestDate;
            cargo.UpdatedById = user.UserId;
            cargo.UpdatedDate = DateTime.UtcNow;

            _unitOfWork.Repository<Cargo>().Update(cargo);
            await _unitOfWork.SaveChangesAsync();
        }
    }
}
