<p-dialog [draggable]="false" [closable]="false" header="Update Cargo Hire" [modal]="true" [(visible)]="dialogVisible"
          [style]="{ width: '1040px' }">
    <ng-template pTemplate="content">
        <div class="cargo-hire-update-dialog">
            <div class="d-flex flex-direction-column h-100">
                <form class="mt-10" [formGroup]="cargoHireForm" (ngSubmit)="submit()">
                    <div class="flex-1 d-flex">
                        <div class="flex-1 p-20">
                            <div class="summary">
                                <div class="mb-10 d-flex align-items-center">
                                    <span class="field_label f-bold mr-10">Type</span>
                                    <span class="field-value">{{ hireRequestCargo()?.cargoUnitType }}</span>
                                </div>
                                <div class="mb-10 d-flex align-items-center">
                                    <span class="field_label  f-bold mr-10">Client</span>
                                    <span class="field_value">{{ hireRequestCargo()?.clientName }}</span>
                                </div>
                                <div class="mb-10 d-flex align-items-center">
                                    <span class="field_label f-bold mr-10">Billing Asset</span>
                                    <span class="field_value">{{ hireRequestCargo()?.billingAssetName }}</span>
                                </div>
                                <div class="mb-10 d-flex align-items-center">
                                    <span class="field_label f-bold mr-10">On-Hired</span>
                                    <span class="field_value">{{ hireRequestCargo()?.onHiredDate | date: 'dd/MM/yyyy'
                                        }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex-1 p-20">
                            <div class="d-flex flex-wrap gap-16" *ngIf="hasShippedInfo()">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Off-Hired Date</span>
                                        <p-calendar
                                                    [inputId]="'offHiredDate'"
                                                    [tabindex]="0"
                                                    [showIcon]="true"
                                                    dateFormat="dd/mm/yy"
                                                    formControlName="offHiredDate"
                                                    [showOnFocus]="hasValidDatesForOffHiring()"
                                                    [readonlyInput]="!hasValidDatesForOffHiring()"
                                                    (keydown)="$event.stopPropagation()"
                                                    [minDate]="minOffHiredDate()"
                                                    appendTo="body">
                                        </p-calendar>
                                    </div>
                                </div>
                            <div class="shipped_info" *ngIf="!hasShippedInfo()">
                                <div class="d-flex flex-wrap gap-16">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Vendor-Outbound Date</span>
                                        <p-calendar
                                                    [inputId]="'vendorOutboundDate'"
                                                    [tabindex]="0"
                                                    [showIcon]="true"
                                                    dateFormat="dd/mm/yy"
                                                    [readonlyInput]="false"
                                                    formControlName="vendorOutboundDate"
                                                    (keydown)="$event.stopPropagation()"
                                                    appendTo="body">
                                        </p-calendar>
                                    </div>
                                </div>
                                <div class="d-flex flex-wrap gap-16">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Vendor-Outbound</span>
                                        <input
                                               type="text"
                                               pInputText
                                               formControlName="vendorOutbound" />
                                    </div>
                                </div>
                                <div class="d-flex flex-wrap gap-16">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Shipped</span>
                                        <p-calendar
                                                    [inputId]="'shipped'"
                                                    [tabindex]="0"
                                                    [showIcon]="true"
                                                    dateFormat="dd/mm/yy"
                                                    [readonlyInput]="false"
                                                    formControlName="shipped"
                                                    (keydown)="$event.stopPropagation()"
                                                    appendTo="body">
                                        </p-calendar>
                                    </div>
                                </div>
                                <div class="d-flex flex-wrap gap-16">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Manifest (Out)</span>
                                        <input
                                               type="text"
                                               pInputText
                                               formControlName="manifestOut" />
                                    </div>
                                </div>
                                <div class="d-flex flex-wrap gap-16">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Asset</span>
                                        <p-dropdown
                                                    [options]="assets()"
                                                    [filter]="true"
                                                    formControlName="assetId"
                                                    styleClass="new-version"
                                                    optionLabel="name"
                                                    optionValue="assetId"
                                                    inputId="asset"
                                                    [showClear]="true"
                                                    appendTo="body" />
                                    </div>
                                </div>
                            </div>
                            <div class="returned_info" *ngIf="hasShippedInfo()">
                                <div class="d-flex flex-wrap gap-16">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Returned</span>
                                        <p-calendar
                                                    [inputId]="'returned'"
                                                    [tabindex]="0"
                                                    [showIcon]="true"
                                                    dateFormat="dd/mm/yy"
                                                    [readonlyInput]="false"
                                                    formControlName="returned"
                                                    (keydown)="$event.stopPropagation()"
                                                    [minDate]="minReturnedDate()"
                                                    appendTo="body">
                                        </p-calendar>
                                    </div>
                                </div>
                                <div class="d-flex flex-wrap gap-16">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Manifest (In)</span>
                                        <input
                                               type="text"
                                               pInputText
                                               formControlName="manifestIn" />
                                    </div>
                                </div>
                                <div class="d-flex flex-wrap gap-16">
                                    <div class="flex-column p-10 flex-1">
                                        <span>Vendor-Inbound</span>
                                        <input
                                               type="text"
                                               pInputText
                                               formControlName="vendorInbound" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <button
                class="btn-tertiary"
                type="button"
                (click)="hideDialog()">
            Cancel
        </button>
        <button
                class="btn-primary"
                type="button"
                (click)="submit()"
                [disabled]="!cargoHireForm.valid || cargoHireForm.pristine">
            Save
        </button>
    </ng-template>
</p-dialog>