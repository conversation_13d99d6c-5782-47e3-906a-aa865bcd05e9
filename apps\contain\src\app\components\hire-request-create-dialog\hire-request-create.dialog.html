<p-dialog [draggable]="false" header="Create Hire Request" [modal]="true" [(visible)]="dialogVisible"
          [style]="{ width: '840px' }">
    <ng-template pTemplate="content">
        <div class="hire-request-create-dialog">
            <div class="grid-container">
                <form class="mt-32" [formGroup]="hireRequestForm" (ngSubmit)="submit()">
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Client</span>
                            <p-dropdown
                                        [options]="clients()"
                                        [filter]="true"
                                        formControlName="clientId"
                                        optionLabel="name"
                                        styleClass="new-version"
                                        optionValue="clientId"
                                        inputId="client"
                                        [showClear]="true"
                                        appendTo="body" />
                        </div>
                        <div class="flex-column p-10 flex-1">
                            <span>Asset</span>
                            <p-dropdown
                                        [options]="assets()"
                                        [filter]="true"
                                        formControlName="assetId"
                                        styleClass="new-version"
                                        optionLabel="name"
                                        optionValue="assetId"
                                        inputId="asset"
                                        [showClear]="true"
                                        appendTo="body" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Requested By</span>
                            <input
                                   type="text"
                                   pInputText
                                   label="Requested By"
                                   formControlName="requestedBy" />
                        </div>
                        <div class="flex-column p-10 flex-1">
                            <span>Requested Date</span>
                            <p-calendar
                                        [inputId]="'requestedDate'"
                                        [showIcon]="true"
                                        [tabindex]="0"
                                        [showClear]="true"
                                        [showTime]="false"
                                        [readonlyInput]="false"
                                        dateFormat="dd/mm/yy"
                                        formControlName="requestedDate"
                                        (keydown)="$event.stopPropagation()"
                                        appendTo="body">
                            </p-calendar>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Planned Sailing Date</span>
                            <p-calendar
                                        [inputId]="'plannedSailingDate'"
                                        [showIcon]="true"
                                        [tabindex]="0"
                                        [showTime]="true"
                                        [readonlyInput]="false"
                                        dateFormat="dd/mm/yy"
                                        [showClear]="true"
                                        formControlName="plannedSailingDate"
                                        (keydown)="$event.stopPropagation()"
                                        appendTo="body">
                            </p-calendar>
                        </div>
                    </div>
                </form>
            </div>

            <hire-request-units
                                *ngIf="hireRequestUnits?.length"
                                [hireRequestForm]="hireRequestForm"></hire-request-units>

            <div class="p-dialog-header header">
                <span class="p-dialog-title">Add Units</span>
            </div>
            <p-divider></p-divider>
            <div class="grid-container">
                <form class="mt-32" [formGroup]="hireRequestUnitForm" (ngSubmit)="submit()">
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <span>Description</span>
                            <p-dropdown
                                        [options]="cargoDescriptions()"
                                        [filter]="true"
                                        formControlName="cargoDescriptionId"
                                        optionLabel="description"
                                        styleClass="new-version"
                                        optionValue="cargoDescriptionId"
                                        inputId="cargoDescriptionId"
                                        [showClear]="true"
                                        appendTo="body" />
                        </div>
                        <div class="flex-column p-10 flex-1">
                            <span>Unit Quantity</span>
                            <p-inputNumber inputId="integeronly" formControlName="unitQuantity" />
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <p-checkbox
                                        formControlName="cstRequired"
                                        inputId="cstRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="cstRequired" class="fs-14 color-dark-gray">CST Required</label>
                        </div>
                        <div class="flex-column p-10 flex-1">
                            <p-checkbox
                                        formControlName="doorsRequired"
                                        inputId="doorsRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="doorsRequired" class="fs-14 color-dark-gray">Doors Required</label>
                        </div>

                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <p-checkbox
                                        formControlName="removableSidesRequired"
                                        inputId="removableSidesRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="removableSidesRequired" class="fs-14 color-dark-gray">Removable-Sides
                                Required</label>
                        </div>
                        <div class="flex-column p-10 flex-1">
                            <p-checkbox
                                        formControlName="netRequired"
                                        inputId="netRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="netRequired" class="fs-14 color-dark-gray">Net Required</label>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-16">
                        <div class="flex-column p-10 flex-1">
                            <p-checkbox
                                        formControlName="tarpaulinRequired"
                                        inputId="tarpaulinRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="tarpaulinRequired" class="fs-14 color-dark-gray">Tarp Required</label>
                        </div>
                        <div class="flex-column p-10 flex-1">
                            <p-checkbox
                                        formControlName="shelvesRequired"
                                        inputId="shelvesRequired"
                                        [binary]="true"></p-checkbox>
                            <label for="shelvesRequired" class="fs-14 color-dark-gray">Shelves Required</label>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end pt-20 gap-16">
                        <div class="flex-column p-10 flex-1">
                            <label for="comments-box">Comments</label>
                            <textarea
                                      id="comments-box"
                                      rows="5"
                                      cols="30"
                                      style="resize: none;"
                                      pInputTextarea
                                      formControlName="comments">
                            </textarea>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end pt-20 gap-16">
                        <button
                                class="btn-primary"
                                type="button"
                                (click)="addUnit()"
                                [disabled]="!hireRequestUnitForm.valid">
                            Add Unit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <button
                class="btn-tertiary"
                type="button"
                (click)="hideDialog()">
            Cancel
        </button>
        <button
                class="btn-primary"
                type="button"
                (click)="submit()"
                [disabled]="!hireRequestForm.valid">
            Create Hire Request
        </button>
    </ng-template>
</p-dialog>