<div class="waiting-list-table">
    <div class="d-flex justify-content-between align-items-center pb-20 gap-4">
        <h2>Hire Request Waiting List</h2>
        <button class="btn-primary" type="button" (click)="setCreateDialogVisible()">
            Create
          </button>
    </div>

    <contain-hire-request-filters
    [isHireRequestWaitingList]="true">
    </contain-hire-request-filters>

    <p-table 
        [columns]="listColumns()" 
        [value]="rows!"  
        [scrollable]="true"
        scrollHeight="calc(100vh - 273px)"
        [rowsPerPageOptions]="[20, 25, 50]"
        [paginator]="true"
        [rows]="20"
        >
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let column of columns" scope="col" [style.min-width.px]="column.width"
                    [style.width.%]="(column.width / tableWidth) * 100">
                    <span>{{ column.name }}</span>
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
            <tr [ngClass]="item.isCancelled ? 'cancelled' : item.isHighlighted ? 'background-color: #b0bed9' : ''">
                <td>
                    <p-checkbox 
                    [(ngModel)]="item.isHighlighted"
                    [id]="item.hireRequestId" 
                    (onChange)="setIsHighlighted(item.hireRequestId)"
                    [value]="item.isHighlighted"
                    [binary]="true"
                    [inputId]="item.hireRequestId">
                    </p-checkbox>
                    </td>
                    <td>{{item.requestedDate | date:'dd/MM/yyyy' }}</td>
                    <td>{{item.plannedSailingDate | date:'dd/MM/yyyy' }}</td>
                    <td>{{item.requestedBy }}</td>
                    <td>{{item.cargoDescription }}</td>
                    <td>{{item.unitQuantity }}</td>
                    <td>{{item.clientName }}</td>
                    <td>{{item.assetName }}</td>
                    <td>{{item.cstRequired ? 'Yes' : 'No' }}</td>
                    <td>{{item.doorsRequired ? 'Yes' : 'No' }}</td>
                    <td>{{item.removableSidesRequired ? 'Yes' : 'No' }}</td>
                    <td>{{item.netRequired ? 'Yes' : 'No' }}</td>
                    <td>{{item.tarpaulinRequired ? 'Yes' : 'No' }}</td>
                    <td>{{item.shelvesRequired ? 'Yes' : 'No' }}</td>
                    <td>{{item.vendorVendorName }}</td>
                    <td>{{item.allocatedUnits }}</td> 
                    <td>
                        <i 
                            title="Open Hire Request Details"
                            class="pi pi-info-circle"  
                            (click)="openDetailsDialog(item)">
                            </i>
                        </td>
                    </tr>
        </ng-template>
    </p-table>

    <contain-hire-request-details-dialog
    *ngIf="detailsDialogVisible && selectedHireRequest"  
    [dialogVisible]="detailsDialogVisible"
    (dialogToggle)="setDialogVisible()"
    [hireRequestInput]="selectedHireRequest"
    [isHireRequestWaitingList]="true">        
    </contain-hire-request-details-dialog>
    
    <contain-hire-request-create-dialog
    *ngIf="createDialogVisible"  
    [dialogVisible]="createDialogVisible"
    (dialogToggle)="setCreateDialogVisible()">        
    </contain-hire-request-create-dialog>
</div>