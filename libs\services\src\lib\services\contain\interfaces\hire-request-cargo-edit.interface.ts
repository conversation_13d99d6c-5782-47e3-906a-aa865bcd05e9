export interface HireRequestCargoEdit {
    vendorId: string;
    cargoCCUId: string;
    cargoUnitType: string;
    clientId: string;
    billingAssetId: string;
    onHiredDate?: Date;
    vendorOutboundDate?: Date;
    shipped?: Date;
    returned?: Date;
    vendorInboundDate?: Date;
    offHiredDate?: Date;
    reference: string;
    manifestOut: string;
    manifestIn: string;
    assetId: string;
    vendorOutbound: string;
    vendorInbound: string;
    consignmentNumber: string;
    containerPoolId: string;
    longTermHire?: boolean;
    shelvesSupplied?: boolean;
    shelvesReturned?: boolean;
    netSupplied?: boolean;
    netReturned?: boolean;
    tarpaulinSupplied?: boolean;
    tarpaulinReturned?: boolean;
    isPool: boolean;
}