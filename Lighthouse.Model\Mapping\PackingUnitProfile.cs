﻿namespace Lighthouse.Model.Mapping
{
    public class PackingUnitProfile : Profile
    {
        public PackingUnitProfile()
        {

            CreateMap<PackingUnit, PackingUnitModel>()
                .ForMember(d => d.CreatedByName, s => s.MapFrom(p => p.CreatedBy.Deleted ? "Deleted User" : $"{p.CreatedBy.Firstname} {p.CreatedBy.Lastname}"))
                .ForMember(d => d.UpdatedByName, s => s.MapFrom(p => p.UpdatedById == null ? "" : p.UpdatedBy.Deleted ? "Deleted User" : $"{p.UpdatedBy.Firstname} {p.UpdatedBy.Lastname}"));

            CreateMap<PackingUnitUpsertModel, PackingUnit>();
        }
    }
}