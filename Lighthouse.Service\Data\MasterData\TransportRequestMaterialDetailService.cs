namespace Lighthouse.Service.Data.MasterData
{
    public class TransportRequestMaterialDetailService : ITransportRequestMaterialDetailService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IExportService _exportService;
        private readonly ITransportRequestMaterialDetailDangerousGoodService _transportRequestMaterialDetailDangerousGoodService;
        private readonly IVoyageCargoWeightUtility _voyageCargoWeightUtility;
        private readonly ILocationService _locationService;
        private readonly IUserService _userService;

        public TransportRequestMaterialDetailService(IUnitOfWork unitOfWork,
            IMapper mapper,
            IExportService exportService,
            ITransportRequestMaterialDetailDangerousGoodService transportRequestMaterialDetailDangerousGoodService,
            IVoyageCargoWeightUtility voyageCargoWeightUtility,
            ILocationService locationService,
            IUserService userService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _exportService = exportService;
            _transportRequestMaterialDetailDangerousGoodService = transportRequestMaterialDetailDangerousGoodService;
            _voyageCargoWeightUtility = voyageCargoWeightUtility;
            _locationService = locationService;
            _userService = userService;
        }
        public async Task<IList<TransportRequestMaterialDetailModel>> GetAsync()
        {
            var TransportRequestMaterialDetails = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.TransportRequest)
                .Include(x => x.TransportRequestMaterialDetailDangerousGood)
                .Include(x => x.TransportRequestCargo)
                .Include(x => x.TransportRequestBulkCargo)
                .ToListAsync();

            return _mapper.Map<List<TransportRequestMaterialDetailModel>>(TransportRequestMaterialDetails);
        }

        public async Task<IList<TransportRequestMaterialDetailModel>> GetByTransportRequestIdAsync(Guid transportRequestId)
        {
            var transportRequestMaterialDetails = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.TransportRequest)

                .Include(x => x.TransportRequestMaterialDetailDangerousGood)
                    .ThenInclude(x => x.TransportRequestCargoDangerousGood)
                        .ThenInclude(x => x.DangerousGood)


                .Include(x => x.TransportRequestMaterialDetailDangerousGood)
                    .ThenInclude(x => x.TransportRequestBulkCargoDangerousGood)
                        .ThenInclude(x => x.DangerousGood)

                .Include(x => x.TransportRequestMaterialDetailAttachments)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.Cargo)
                        .ThenInclude(x => x.Vendor)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.ToAsset)
                    .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.ToLocation)


                .Include(x => x.TransportRequestBulkCargo)
                    .ThenInclude(x => x.TransportRequestBulkCargoDangerousGood)
                .Include(x => x.TransportRequestBulkCargo)

                    .ThenInclude(x => x.BulkType)
                .Include(x => x.TransportRequestBulkCargo)
                    .ThenInclude(x => x.FromAsset)
                    .Include(x => x.TransportRequestBulkCargo)
                    .ThenInclude(x => x.FromLocation)
                .Include(x => x.TransportRequestBulkCargo)
                    .ThenInclude(x => x.ToAsset)
                    .Include(x => x.TransportRequestBulkCargo)
                    .ThenInclude(x => x.ToLocation)
                .Where(x => x.TransportRequestId == transportRequestId)
                .OrderBy(x => x.CreatedDate)
                .ToListAsync();

            var result = _mapper.Map<List<TransportRequestMaterialDetailModel>>(transportRequestMaterialDetails);

            foreach (var materialDetail in result)
            {
                if (materialDetail.TransportRequestCargo != null && materialDetail.TransportRequestCargo.Cargo != null)
                {
                    var locationById = await _locationService.GetLocationByIdAsync(materialDetail.TransportRequestCargo.Cargo.LocationId);
                    var measurementUnit = locationById.MeasurementUnit;

                    materialDetail.EstimatedWeight = (double)_voyageCargoWeightUtility.ConvertWeight(materialDetail.EstimatedWeight, measurementUnit, true);

                    if (materialDetail.TransportRequestCargo.EstimatedCargoWeight.HasValue)
                    {
                        materialDetail.TransportRequestCargo.EstimatedCargoWeight = (double)_voyageCargoWeightUtility.ConvertWeight(
                            materialDetail.TransportRequestCargo.EstimatedCargoWeight, measurementUnit, true);
                    }
                }
            }

            return result;
        }

        public async Task<IList<TransportRequestMaterialDetailModel>> GetLatestSubmittedVersionByTransportRequestIdAsync(Guid transportRequestId)
        {
            var latestCargoSnapshot = await _unitOfWork.Repository<TransportRequestMaterialDetailSnapshot>()
                .Query(q => q.TransportRequestId == transportRequestId)
                .OrderByDescending(o => o.CreatedDate)
                .FirstOrDefaultAsync();

            if (latestCargoSnapshot == null)
            {
                return new List<TransportRequestMaterialDetailModel>();
            }

            var materialDetails = JsonSerializer.Deserialize<List<TransportRequestMaterialDetailModel>>(latestCargoSnapshot.Content)
                .OrderByDescending(o => o.CreatedDate)
                .ToList();

            return materialDetails;
        }

        public async Task<TransportRequestMaterialDetailModel> GetByIdAsync(Guid id)
        {
            var TransportRequestMaterialDetail = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
               .Query()
               .AsNoTracking()
               .AsSplitQuery()
               .Include(x => x.TransportRequest)
               .Include(x => x.TransportRequestMaterialDetailDangerousGood)
               .Include(x => x.TransportRequestCargo)
               .Include(x => x.TransportRequestBulkCargo)
               .Where(x => x.TransportRequestMaterialDetailId == id)
               .SingleOrDefaultAsync();
            return _mapper.Map<TransportRequestMaterialDetailModel>(TransportRequestMaterialDetail);
        }

        public async Task<TransportRequestMaterialDetailModel> CreateAsync(TransportRequestMaterialDetailUpsertModel model, UserModel currentUser)
        {
            await _unitOfWork.BeginTransactionAsync();

            var transportRequestMaterialDetail = _mapper.Map<TransportRequestMaterialDetail>(model);

            transportRequestMaterialDetail.CreatedById = currentUser.UserId;

            var locationById = await _locationService.GetLocationByIdAsync(currentUser.LocationId.Value);
            transportRequestMaterialDetail.EstimatedWeight = (double)_voyageCargoWeightUtility.ConvertWeight(transportRequestMaterialDetail.EstimatedWeight, locationById.MeasurementUnit, false);

            transportRequestMaterialDetail = await _unitOfWork.Repository<TransportRequestMaterialDetail>().CreateAsync(transportRequestMaterialDetail);

            await _unitOfWork.SaveChangesAsync();

            var mdDangerousGood = new TransportRequestMaterialDetailDangerousGoodModel();

            if (transportRequestMaterialDetail.TransportRequestBulkCargoId != null)
            {
                var bulkCargo = await GetTransportRequestBulkCargo(transportRequestMaterialDetail.TransportRequestBulkCargoId.Value);
                var materialDetailDangerousGood = new TransportRequestMaterialDetailDangerousGoodUpsertModel()
                {
                    TransportRequestBulkCargoDangerousGoodId = bulkCargo.TransportRequestBulkCargoDangerousGoodId,
                    TransportRequestCargoDangerousGoodId = null
                };
                await _transportRequestMaterialDetailDangerousGoodService.AssignMdDgAsync(transportRequestMaterialDetail.TransportRequestMaterialDetailId, materialDetailDangerousGood);
            }

            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(transportRequestMaterialDetail.TransportRequestMaterialDetailId);
        }

        public async Task<List<TransportRequestMaterialDetailModel>> BulkUpdateAsync(Guid transportRequestId, List<TransportRequestMaterialDetailUpsertModel> finalTotalList, UserModel currentUser)
        {
            if (
                finalTotalList.Where(x => x.TransportRequestMaterialDetailId.HasValue)
                    .Count() != finalTotalList
                    .Where(x => x.TransportRequestMaterialDetailId.HasValue)
                    .DistinctBy(x => x.TransportRequestMaterialDetailId)
                    .Count()
               )
            {
                throw new Exception("Duplicate Entry in the list");
            }

            await _unitOfWork.BeginTransactionAsync();

            var currentTRMaterialDetailes = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query(x => x.TransportRequestId == transportRequestId)
                .ToListAsync();

            var measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitAsync(transportRequestId);

            var itemsToDelete = currentTRMaterialDetailes
                .Where(x => !finalTotalList
                    .Any(l => l.TransportRequestMaterialDetailId == x.TransportRequestMaterialDetailId))
                .ToList();

            foreach (var trMaterialDetailToDelete in itemsToDelete)
            {
                trMaterialDetailToDelete.Deleted = true;
                currentTRMaterialDetailes.Remove(trMaterialDetailToDelete);
            }
            await _unitOfWork.SaveChangesAsync();

            var listToCreate = new List<TransportRequestMaterialDetail>();

            var location = await _unitOfWork.Repository<Location>().Query(x => x.LocationId == currentUser.LocationId).FirstOrDefaultAsync();

            foreach (var trMaterialDetailToUpsert in finalTotalList)
            {
                if (trMaterialDetailToUpsert.TransportRequestBulkCargoId.HasValue && trMaterialDetailToUpsert.TransportRequestCargoId.HasValue)
                    throw new Exception("Only one Cargo Bulk or Cargo can be selected");
                if (!trMaterialDetailToUpsert.TransportRequestBulkCargoId.HasValue && !trMaterialDetailToUpsert.TransportRequestCargoId.HasValue)
                    throw new Exception("A cargo bulk or a cargo is required.");

                if (trMaterialDetailToUpsert.TransportRequestMaterialDetailId.HasValue) //if it's update
                {
                    var shouldBeChanged = false;
                    var trMaterialDetail = currentTRMaterialDetailes.SingleOrDefault(x => x.TransportRequestMaterialDetailId == trMaterialDetailToUpsert.TransportRequestMaterialDetailId);

                    if (trMaterialDetail == null)
                        throw new Exception($"Some material detail items do not belong to this transport request and/or may not exist");

                    if ((trMaterialDetailToUpsert.Status == TransportRequestCargoStatus.Submitted)
                    && (trMaterialDetailToUpsert.Comments != trMaterialDetail.Comments
                    || trMaterialDetailToUpsert.EstimatedWeight != trMaterialDetail.EstimatedWeight
                    || trMaterialDetailToUpsert.Emballage != trMaterialDetail.Emballage
                    || trMaterialDetailToUpsert.PackingUnitId != trMaterialDetail.PackingUnit?.PackingUnitId
                    || trMaterialDetailToUpsert.Quantity != trMaterialDetail.Quantity
                    || trMaterialDetailToUpsert.SupplyQuantity != trMaterialDetail.SupplyQuantity
                    || trMaterialDetailToUpsert.Description != trMaterialDetail.Description
                    || trMaterialDetailToUpsert.CountryOfOrigin != trMaterialDetail.CountryOfOrigin
                    || trMaterialDetailToUpsert.PoNumber != trMaterialDetail.PoNumber
                    || trMaterialDetailToUpsert.WorkOrder != trMaterialDetail.WorkOrder
                    || trMaterialDetailToUpsert.PickupLocation != trMaterialDetail.PickupLocation
                    || trMaterialDetailToUpsert.CustomsDocumentNumber != trMaterialDetail.CustomsDocumentNumber
                    || trMaterialDetailToUpsert.CustomsDocumentDate != trMaterialDetail.CustomsDocumentDate
                    || trMaterialDetailToUpsert.CommodityCode != trMaterialDetail.CommodityCode
                    || trMaterialDetailToUpsert.SerialNumber != trMaterialDetail.SerialNumber
                    || trMaterialDetailToUpsert.Waste != trMaterialDetail.Waste
                    || trMaterialDetailToUpsert.EWC != trMaterialDetail.EWC
                    || trMaterialDetailToUpsert.WasteDescription != trMaterialDetail.WasteDescription
                    || trMaterialDetailToUpsert.ManifestNumber != trMaterialDetail.ManifestNumber
                    || trMaterialDetailToUpsert.Value != trMaterialDetail.Value
                    || trMaterialDetailToUpsert.Requestor != trMaterialDetail.Requestor
                    || trMaterialDetailToUpsert.MaxStockQuantity != trMaterialDetail.MaxStockQuantity
                    || trMaterialDetailToUpsert.ProperShippingName != trMaterialDetail.ProperShippingName
                    || trMaterialDetailToUpsert.MaxStockValue != trMaterialDetail.MaxStockValue
                    || trMaterialDetailToUpsert.StockMaterialReturnedAs != trMaterialDetail.StockMaterialReturnedAs
                    || trMaterialDetailToUpsert.ParentCargoItemStatus != trMaterialDetail.ParentCargoItemStatus
                    || trMaterialDetailToUpsert.ParentCargoItemCategory != trMaterialDetail.ParentCargoItemCategory
                    || trMaterialDetailToUpsert.Status != trMaterialDetail.Status
                    || trMaterialDetailToUpsert.PackingGroup != trMaterialDetail.PackingGroup
                    || trMaterialDetailToUpsert.CustomsEntryType != trMaterialDetail.CustomsEntryType
                    || trMaterialDetailToUpsert.CustomsStatus != trMaterialDetail.CustomsStatus
                    || trMaterialDetailToUpsert.SupplyUnitType != trMaterialDetail.SupplyUnitType
                    || trMaterialDetailToUpsert.ChangeReason != trMaterialDetail.ChangeReason
                    || trMaterialDetailToUpsert.RejectedReason != trMaterialDetail.RejectedReason
                    )) shouldBeChanged = true;

                    _mapper.Map(trMaterialDetailToUpsert, trMaterialDetail);

                    if (shouldBeChanged)
                    {
                        trMaterialDetail.Status = TransportRequestCargoStatus.Changed;
                    }

                    trMaterialDetail.UpdatedById = currentUser.UserId;
                    trMaterialDetail.UpdatedDate = DateTime.UtcNow;

                    trMaterialDetail.EstimatedWeight = (double)_voyageCargoWeightUtility.ConvertWeight(trMaterialDetail.EstimatedWeight, location.MeasurementUnit, false);
                }
                else //if it's create
                {
                    var trMaterialDetail = _mapper.Map<TransportRequestMaterialDetail>(trMaterialDetailToUpsert);

                    _mapper.Map(trMaterialDetailToUpsert, trMaterialDetail);

                    trMaterialDetail.TransportRequestMaterialDetailId = Guid.NewGuid();
                    trMaterialDetail.CreatedById = currentUser.UserId;
                    trMaterialDetail.CreatedDate = DateTime.UtcNow;
                    trMaterialDetail.Status = TransportRequestCargoStatus.Draft;
                    trMaterialDetail.EstimatedWeight = (double)_voyageCargoWeightUtility.ConvertWeight(trMaterialDetail.EstimatedWeight, location.MeasurementUnit, false);

                    listToCreate.Add(trMaterialDetail);
                }
            }

            await _unitOfWork.SaveChangesAsync(); //this applies the updates

            await _unitOfWork.Repository<TransportRequestMaterialDetail>().BulkCreateAsync(listToCreate);

            await _unitOfWork.SaveChangesAsync(); //this applies the creates

            await _unitOfWork.CommitAsync();
            return (List<TransportRequestMaterialDetailModel>)await GetByTransportRequestIdAsync(transportRequestId);
        }

        public async Task<TransportRequestMaterialDetailModel> UpdateAsync(Guid id, TransportRequestMaterialDetailUpsertModel model, UserModel currentUser)
        {
            await _unitOfWork.BeginTransactionAsync();

            var transportRequestMaterialDetail = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query()
                .AsSplitQuery()
                .SingleOrDefaultAsync(x => x.TransportRequestMaterialDetailId == id);

            TransportRequestMaterialDetailDangerousGoodModel mdDangerousGood = null;
            TransportRequestMaterialDetailModel updatedMaterialDetail = null;

            if (transportRequestMaterialDetail.TransportRequestCargoId != model.TransportRequestCargoId && model.TransportRequestBulkCargoId != null ||
                model.TransportRequestBulkCargoId != transportRequestMaterialDetail.TransportRequestBulkCargoId)
            {
                if (transportRequestMaterialDetail.TransportRequestMaterialDetailDangerousGoodId != null)
                {
                    updatedMaterialDetail = await _transportRequestMaterialDetailDangerousGoodService.DeleteAsync(transportRequestMaterialDetail.TransportRequestMaterialDetailId);
                }

                if (model.TransportRequestCargoId == null && model.TransportRequestBulkCargoId.HasValue)
                {
                    var bulkCargo = await GetTransportRequestBulkCargo(model.TransportRequestBulkCargoId.Value);
                    if (bulkCargo.TransportRequestBulkCargoDangerousGoodId != null)
                    {
                        var materialDetailDangerousGood = new TransportRequestMaterialDetailDangerousGoodUpsertModel
                        {
                            TransportRequestBulkCargoDangerousGoodId = bulkCargo.TransportRequestBulkCargoDangerousGoodId,
                            TransportRequestCargoDangerousGoodId = null
                        };

                        mdDangerousGood = await _transportRequestMaterialDetailDangerousGoodService.AssignMdDgAsync(
                            transportRequestMaterialDetail.TransportRequestMaterialDetailId,
                            materialDetailDangerousGood
                        );
                    }
                }
            }

            transportRequestMaterialDetail = _mapper.Map(model, transportRequestMaterialDetail);

            transportRequestMaterialDetail.TransportRequestMaterialDetailDangerousGoodId = mdDangerousGood?.TransportRequestMaterialDetailDangerousGoodId ??
                (updatedMaterialDetail?.TransportRequestMaterialDetailId != Guid.Empty ? (Guid?)null : transportRequestMaterialDetail.TransportRequestMaterialDetailDangerousGoodId);

            transportRequestMaterialDetail.UpdatedById = currentUser.UserId;
            transportRequestMaterialDetail.UpdatedDate = DateTime.UtcNow;

            var locationById = await _locationService.GetLocationByIdAsync(currentUser.LocationId.Value);
            transportRequestMaterialDetail.EstimatedWeight = (double)_voyageCargoWeightUtility.ConvertWeight(transportRequestMaterialDetail.EstimatedWeight, locationById.MeasurementUnit, false);

            _unitOfWork.Repository<TransportRequestMaterialDetail>().Update(transportRequestMaterialDetail);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(transportRequestMaterialDetail.TransportRequestMaterialDetailId);
        }

        public async Task ReinstateAsync(List<Guid> transportRequestMaterialDetailIds)
        {
            var user = await _userService.GetCurrentUser();

            var transportRequestMaterialDetails = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query(x => transportRequestMaterialDetailIds.Contains(x.TransportRequestMaterialDetailId))
                .Include(x => x.TransportRequestBulkCargo)
                .Include(x => x.TransportRequestCargo)
                .ToListAsync();

            var materialDetailsThatCantBeReinstated = transportRequestMaterialDetails
                 .Where(x => (x.TransportRequestCargo is not null && x.TransportRequestCargo.IsCancelled)
                   || (x.TransportRequestBulkCargo is not null && x.TransportRequestBulkCargo.IsCancelled)
                 ).Count();

            var materialDetailsViableToReinstate = transportRequestMaterialDetails
                .Where(x => (x.TransportRequestCargo is not null && !x.TransportRequestCargo.IsCancelled)
                   || (x.TransportRequestBulkCargo is not null && !x.TransportRequestBulkCargo.IsCancelled)
                ).ToList();

            foreach (var materialDetailViableToReinstate in materialDetailsViableToReinstate)
            {
                if (materialDetailViableToReinstate.Status != TransportRequestCargoStatus.Draft)
                    materialDetailViableToReinstate.Status = TransportRequestCargoStatus.Changed;

                materialDetailViableToReinstate.IsCancelled = false;
                materialDetailViableToReinstate.UpdatedDate = DateTime.UtcNow;
                materialDetailViableToReinstate.UpdatedById = user.UserId;
                materialDetailViableToReinstate.CancellationReason = null;
            }

            if (materialDetailsThatCantBeReinstated > 0)
            {
                if (materialDetailsThatCantBeReinstated == 1)
                {
                    throw new Exception("A material detail could not be reinstated due to associated Cancelled Cargo or Bulk.");
                } else
                {
                    throw new Exception($"{materialDetailsThatCantBeReinstated} Material Details could not be reinstated due to associated Cancelled Cargoes or Bulks.");
                }
            }
            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<bool> BulkDeleteAsync(List<Guid> transportRequestMaterialDetailIds)
        {
            await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query(x => transportRequestMaterialDetailIds.Contains(x.TransportRequestMaterialDetailId))
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(x => x.UpdatedDate, DateTime.UtcNow)
                    .SetProperty(x => x.Deleted, true)
                );

            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<bool> UpdateTransportRequestCargoCommentAsync(TransportRequestMaterialDetailCommentModel cargoCommentModel)
        {
            await _unitOfWork.BeginTransactionAsync();

            await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query(x => x.TransportRequestMaterialDetailId == cargoCommentModel.TransportRequestMaterialDetailId)
                .ExecuteUpdateAsync(x => x
                    .SetProperty(p => p.Comments, cargoCommentModel.Comment)
                );

            await _unitOfWork.CommitAsync();

            return true;
        }

        public async Task<bool> UpdateTransportRequestToDoImdgCompleteAsync(Guid id, TransportRequestMaterialDetailIMDGModel imdgModel)
        {
            await _unitOfWork.BeginTransactionAsync();

            await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query(x => x.TransportRequestMaterialDetailId == id)
                .ExecuteUpdateAsync(x => x
                    .SetProperty(p => p.ToDoIMDGComplete, imdgModel.Complete)
                    .SetProperty(p => p.RejectedReason, imdgModel.RejectReason)
                );

            await _unitOfWork.CommitAsync();

            return true;


        }

        public async Task<bool> CancelAsync(TransportRequestCancellationReasonModel cancellations)
        {
            var user = await _userService.GetCurrentUser();

            var transportRequestMaterialDetails = await _unitOfWork.Repository<TransportRequestMaterialDetail>().Query(x => cancellations.Ids.Contains(x.TransportRequestMaterialDetailId)).ToListAsync();

            var materialDetailsToDelete = new List<TransportRequestMaterialDetail>();
            var materialDetailsToCancel = new List<TransportRequestMaterialDetail>();

            foreach (var transportRequestMaterialDetail in transportRequestMaterialDetails)
            {
                if (transportRequestMaterialDetail.Status == TransportRequestCargoStatus.Draft)
                {
                    materialDetailsToDelete.Add(transportRequestMaterialDetail);
                }
                else
                {
                    transportRequestMaterialDetail.IsCancelled = true;
                    transportRequestMaterialDetail.CancellationReason = cancellations.CancellationReason;
                    transportRequestMaterialDetail.UpdatedById = user.UserId;
                    transportRequestMaterialDetail.UpdatedDate = DateTime.UtcNow;
                    transportRequestMaterialDetail.Status = TransportRequestCargoStatus.Changed;

                    materialDetailsToCancel.Add(transportRequestMaterialDetail);
                }
            }
            if (materialDetailsToDelete.Any())
            {
                foreach (var materialDetailToDelete in materialDetailsToDelete)
                {
                    materialDetailToDelete.Deleted = true;
                    materialDetailToDelete.UpdatedDate = DateTime.UtcNow;
                    materialDetailToDelete.UpdatedById = user.UserId;
                    await _unitOfWork.SaveChangesAsync();
                }
            }

            if (materialDetailsToCancel.Any())
            {
                await _unitOfWork.Repository<TransportRequestMaterialDetail>().BulkUpdate(materialDetailsToCancel);
                await _unitOfWork.SaveChangesAsync();
            }
            return true;
        }

        public async Task<byte[]> ExportTransportRequestMaterialDetails(Guid id, string timezone)
        {
            var transportRequestMaterialDetails = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query()
                .Where(x => x.TransportRequestId == id)
                .Include(x => x.Vendor)
                .Include(x => x.TransportRequestMaterialDetailAttachments)
                .Include(x => x.TransportRequestMaterialDetailDangerousGood)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.Cargo)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.ViaVendor)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.Vendor)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.VendorWarehouse)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.ViaVendorWarehouse)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.FromAsset)
                    .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.FromLocation)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.ToAsset)
                .Include(x => x.TransportRequestCargo)
                    .ThenInclude(x => x.ToLocation)
                .Include(x => x.TransportRequest)
                .Select(x => _mapper.Map<TransportRequestMaterialDetailModel>(x))
                .ToListAsync();

            return _exportService.ConvertTransportRequestMaterialDetailsToExcel(transportRequestMaterialDetails, timezone);
        }

        private async Task<TransportRequestBulkCargo> GetTransportRequestBulkCargo(Guid transportRequestBulkCargoId)
        {
            return await _unitOfWork.Repository<TransportRequestBulkCargo>()
                .Query()
                .Where(x => x.TransportRequestBulkCargoId == transportRequestBulkCargoId)
                .SingleOrDefaultAsync();
        }
    }
}
