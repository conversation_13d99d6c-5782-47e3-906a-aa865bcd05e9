<p-dialog
  [draggable]="false"
  [closable]="false"
  [modal]="true"
  [(visible)]="dialogVisible"
  [style]="{ width: '1240px' }"
>
  <ng-template pTemplate="header">
    <span class="p-dialog-title"> Cargo Hire Details </span>
  </ng-template>
  <ng-template pTemplate="content">
    <div class="container" *ngIf="hireRequestCargo()!">
      <span class="p-dialog-header">Hire Info</span>
      <p-divider></p-divider>
      <div class="flex-1 d-flex" *ngIf="hireRequestCargo()">
        <div class="flex-1 p-20">
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">CCU Supplier</span>
            <span class="field-value"
              >{{ hireRequestCargo()!!.vendorVendorName }}</span
            >
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">CCU Owner</span>
            <span class="field-value">{{ hireRequestCargo()!.cargoOwner }}</span>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Vendor-Outbound Date</span>
            <span class="flex-1"
              >{{ hireRequestCargo()!.vendorOutboundDate | date:'dd/MM/yyyy'
              }}</span
            >
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Shipped</span>
            <span class="flex-1"
              >{{ hireRequestCargo()!.shipped | date:'dd/MM/yyyy' }}</span
            >
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Returned</span>
            <span class="flex-1"
              >{{ hireRequestCargo()!.returned | date:'dd/MM/yyyy' }}</span
            >
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Vendor-Inbound Date</span>
            <span class="flex-1"
              >{{ hireRequestCargo()!.vendorInboundDate | date:'dd/MM/yyyy'
              }}</span
            >
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Off-Hired</span>
            <span class="flex-1"
              >{{ hireRequestCargo()!.offHiredDate | date:'dd/MM/yyyy' }}</span
            >
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Billing Asset</span>
            <span class="field-value">{{ hireRequestCargo()!.billingAssetName }}</span>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">D365 Project Id</span>
            <span class="field-value"> </span>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">D365 Project Name</span>
            <span class="field-value"> </span>
          </div>
        </div>
        <div class="flex-1 p-20">
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Ref No/Well</span>
            <span class="field-value">{{ hireRequestCargo()!.reference }}</span>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Manifest (Out)</span>
            <span class="field-value">{{ hireRequestCargo()!.manifestOut }}</span>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Manifest (In)</span>
            <span class="field-value">{{ hireRequestCargo()!.manifestIn }}</span>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Asset</span>
            <span class="field-value">{{ hireRequestCargo()!.assetName }}</span>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Vendor-Outbound</span>
            <span class="field-value"
              >{{ hireRequestCargo()!.vendorOutbound }}</span
            >
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Vendor-Inbound</span>
            <span class="field-value"
              >{{ hireRequestCargo()!.vendorInbound }}</span
            >
          </div>
          <div class="mb-10 d-flex align-items-center">
            <span class="field_label f-bold mr-10">Consignment Number</span>
            <span class="field-value"> </span>
          </div>
        </div>
        <div class="flex-1 p-20 buttons_subsection">
          <div class="mb-10 d-flex align-items-center">
            <button
              class="btn-primary mr-8 "
              type="button"
              (click)="updateCargoHire()"
            >
              Update Hire
            </button>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <button
              class="btn-primary mr-8 "
              type="button"
              (click)="editCargoHire()"
            >
              Edit Hire
            </button>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <button
              class="btn-primary mr-8 "
              type="button"
              (click)="createNoteDialog()"
            >
              Create Note
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="d-flex flex-direction-column h-100">
      <span class="p-dialog-header">Hire Events</span>
      <p-divider></p-divider>
      <div class="flex-1 d-flex">
        <div class="flex-1 p-20">
          <p-table
            [columns]="listColumns()"
            [value]="cargoHireEvents()"
            [scrollable]="true"
          >
            <ng-template pTemplate="header" let-columns>
              <tr>
                <th
                  *ngFor="let column of columns"
                  scope="col"
                  [style.min-width.px]="column.width"
                  [style.width.%]="(column.width / tableWidth) * 100"
                >
                  <span>{{ column.name }}</span>
                </th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item>
              <tr>
                <td>{{ eventTypeDescriptions[item.eventType] }}</td>
                <td>{{item.eventDate | date:'dd/MM/yyyy HH:mm' }}</td>
                <td>
                  <span *ngIf="item.eventType === 6" class="f-bold"
                    >{{item.createdUserEmail}}:</span
                  >
                  {{ item.details }}
                </td>
                <td>
                  <i
                    *ngIf="item.eventType === 6"
                    title="Remove"
                    class="pi pi-trash"
                    (click)="onDelete($event,item)"
                  >
                  </i>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template pTemplate="footer">
    <button class="btn-tertiary" (click)="hideDialog()">Cancel</button>
  </ng-template>
</p-dialog>

<contain-update-cargo-hire-dialog
  *ngIf="updateDialogVisible && hireRequestCargo()!?.hireRequestCargoId"
  [dialogVisible]="updateDialogVisible"
  (dialogToggle)="toggleUpdateDialog()"
>
</contain-update-cargo-hire-dialog>

<contain-edit-cargo-hire-dialog
  *ngIf="editDialogVisible && hireRequestCargo()!?.hireRequestCargoId"
  [dialogVisible]="editDialogVisible"
  [hireRequestCargo]="hireRequestCargo()!"
  (dialogToggle)="hideEditDialog($event)"
>
</contain-edit-cargo-hire-dialog>

<contain-cargo-hire-note-dialog
  *ngIf="noteDialogVisible && hireRequestCargo()!?.hireRequestCargoId"
  [dialogVisible]="noteDialogVisible"
  [hireRequestCargo]="hireRequestCargo()!"
  (dialogToggle)="hideNoteDialog()"
>
</contain-cargo-hire-note-dialog>
