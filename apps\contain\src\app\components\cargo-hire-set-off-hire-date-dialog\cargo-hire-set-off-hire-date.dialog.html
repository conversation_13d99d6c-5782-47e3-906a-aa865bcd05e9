<p-dialog [draggable]="false" [closable]="false" [modal]="true"
          [(visible)]="dialogVisible"
          [style]="{ width: '1240px' }">
    <ng-template pTemplate="header">
        <span class="p-dialog-title">
            Update Hire
        </span>
    </ng-template>
    <ng-template pTemplate="content">
        <form class="d-flex flex-direction-column h-100" [formGroup]="cargoHireForm">
            <div class="flex-1 d-flex">
                <div class="flex-1 p-20">
                    <span class="p-dialog-header">Summary</span>
                    <p-divider></p-divider>
                    <div class="mb-10 d-flex align-items-center mt-10">
                        <span class="field_label f-bold mr-10">Type</span>
                        <span class="field-value">{{ hireRequestCargo.cargoUnitType }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Client</span>
                        <span class="field-value">{{ hireRequestCargo.clientName }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Billing Asset</span>
                        <span class="flex-1">{{ hireRequestCargo.billingAssetName }}</span>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <span class="field_label f-bold mr-10">Shipped</span>
                        <span class="flex-1">{{ hireRequestCargo.shipped | date:'dd/MM/yyyy' }}</span>
                    </div>
                </div>
                <div class="flex-1 p-20">
                    <span class="p-dialog-header">Update Hire</span>
                    <p-divider></p-divider>
                    <div class="mb-10 d-flex align-items-center mt-10">
                        <span class="field_label f-bold">Off Hired</span>
                        <p-calendar
                                    [inputId]="'offHiredDate'"
                                    [tabindex]="0"
                                    inputStyleClass="field-value"
                                    [showIcon]="true"
                                    dateFormat="dd/mm/yy"
                                    [showOnFocus]="hasValidDatesForOffHiring()"
                                    [readonlyInput]="!hireRequestCargo.onHiredDate || !hireRequestCargo.returned"
                                    formControlName="offHiredDate"
                                    (keydown)="$event.stopPropagation()"
                                    [minDate]="minOffHiredDate()"
                                    appendTo="body">
                        </p-calendar>
                    </div>
                </div>
                <div class="flex-1 p-20 buttons_subsection">
                    <div class="mb-10 d-flex align-items-center">
                        <button
                                class="btn-tertiary mr-8"
                                type="button"
                                (click)="hideDialog()">
                            Cancel
                        </button>
                    </div>
                    <div class="mb-10 d-flex align-items-center">
                        <button
                                class="btn-primary"
                                type="button"
                                (click)="submit()"
                                [disabled]="!cargoHireForm.valid || cargoHireForm.pristine">
                            Save Hire
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </ng-template>
    <ng-template pTemplate="footer">
        <button class="btn-tertiary" (click)="hideDialog()">Cancel</button>
    </ng-template>
</p-dialog>