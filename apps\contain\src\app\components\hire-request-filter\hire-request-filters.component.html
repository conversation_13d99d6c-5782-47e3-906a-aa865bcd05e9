<div class="hire-request__filter pb-30">
    <form class="mt-32" [formGroup]="filterForm">
        <div class="d-flex flex-wrap filter_container gap-16">
            <div class="d-flex flex-direction-column gap-4">
                <label class="f-bold">From Date</label>
                <p-calendar
                            [showIcon]="true"
                            [tabindex]="0"
                            dateFormat="dd/mm/yy"
                            [showTime]="false"
                            formControlName="from"
                            [readonlyInput]="false"
                            (keydown)="$event.stopPropagation()"
                            appendTo="body">
                </p-calendar>
            </div>
            <div class="d-flex flex-direction-column gap-4">
                <label class="f-bold">To Date</label>
                <p-calendar
                            [showIcon]="true"
                            [tabindex]="0"
                            dateFormat="dd/mm/yy"
                            [showTime]="false"
                            [readonlyInput]="false"
                            formControlName="to"
                            (keydown)="$event.stopPropagation()"
                            appendTo="body">
                </p-calendar>
            </div>
            <div *ngIf="showField(hireRequestConditionalFilterFields.outboundInbound)"
                 class="flex-column p-10 pt-30 spread-evenly">
                <div class="checkbox">
                    <p-checkbox
                                inputId="outbound"
                                formControlName="outbound"
                                [binary]="true"></p-checkbox>
                    <label for="outbound" class="fs-14 color-dark-gray f-bold">Outbound</label>
                </div>
                <div class="checkbox ml-20">
                    <p-checkbox
                                inputId="inbound"
                                formControlName="inbound"
                                [binary]="true"></p-checkbox>
                    <label for="inbound" class="fs-14 color-dark-gray mr-30 f-bold">Inbound</label>
                </div>
            </div>
            <div *ngIf="showField(hireRequestConditionalFilterFields.client)" class="flex-column p-4"
                 style="flex: 0.5;">
                <span class="f-bold">Client</span>
                <p-dropdown
                            [options]="clients()"
                            [filter]="true"
                            formControlName="clientId"
                            optionLabel="name"
                            optionValue="clientId"
                            [showClear]="true"
                            styleClass="new-version"
                            appendTo="body"
                            panelStyleClass="new-version-panel"
                            appendTo="body" />
            </div>
            <div class="d-flex flex-direction-column gap-4">
              <label class="f-bold">Search</label>
                <span class="p-input-icon-left">
                <em class="pi pi-search search-icon"></em>
                <input type="text" formControlName="searchTerm" pInputText />
                </span>

                <div *ngIf="showField(hireRequestConditionalFilterFields.exactMatchOnly)"
                     class="d-flex align-items-center mr-5 mt-10">
                    <p-checkbox
                                inputId="exactMatchOnly"
                                formControlName="exactMatchOnly"
                                [binary]="true"></p-checkbox>
                    <label for="exactMatchOnly" class="fs-14 color-dark-gray f-bold">Exact Match Only</label>
                </div>
            </div>
            <div *ngIf="showField(hireRequestConditionalFilterFields.includeCancelled)" class="flex-column p-10 pt-30">
                <p-checkbox
                            inputId="includeCancelled"
                            formControlName="includeCancelled"
                            [binary]="true"></p-checkbox>
                <label for="includeCancelled" class="fs-14 color-dark-gray f-bold">Include Cancelled</label>
            </div>
            <div *ngIf="showField(hireRequestConditionalFilterFields.hideCreated)" class="flex-column p-10 pt-30">
                <p-checkbox
                            inputId="hideCreated"
                            formControlName="hideCreated"
                            [binary]="true"></p-checkbox>
                <label for="hideCreated" class="fs-14 color-dark-gray f-bold">Hide Hires Created</label>
            </div>
            <div class="flex-column p-10 pt-20">
                <button
                        (click)="clearFilter()"
                        class="btn btn-secondary button_height"
                        type="button">
                    Clear Filters
                </button>
            </div>
        </div>
    </form>
</div>